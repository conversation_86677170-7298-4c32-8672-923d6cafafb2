
services:
  db:
    image: postgres:16
    container_name: postgres_db
    environment:
      POSTGRES_DB: crypto
      POSTGRES_USER: crypto
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5452:5432"
    networks:
      - crypto_net

volumes:
  postgres_data:  # Data volume for PostgreSQL persistence

networks:
  crypto_net:  # Create a network for the app and db containers to communicate
