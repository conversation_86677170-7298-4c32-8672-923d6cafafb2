import json
import os
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from typing import Optional
from app.db.db_executor import DatabaseExecutor
from app.trade_history.trade_history import TradeHistory
from app.scripts.trader import Trader
from app.strategy.ppo_strategy import PPOStrategy
from app.trade_manager.portfolio_manager import PortfolioManager
from app.trade_manager.trade_manager import TradeManager
from app.scripts.base_data_feed import DataFeed
from app.scripts.base_order_executor import OrderExecutor
from app.scripts.historical_data_feed import HistoricalDataFeed
from app.scripts.kraken_data_feed import KrakenDataFeed
from app.scripts.simulated_order_executor import SimulatedOrderExecutor
from app.scripts.real_order_executor import RealOrderExecutor
from app.backtester.backtestAnalyzer import BacktestAnalyzer
from app.exchange.kraken_api import KrakenAPI

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Retrieve database credentials from environment variables
DB_NAME = os.getenv('DB_NAME')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_HOST = os.getenv('DB_HOST')

# Retrieve Kraken API credentials and URLs
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_API_SECRET = os.getenv('KRAKEN_API_SECRET')
API_URL = os.getenv('API_URL')
WS_URL = os.getenv('WS_URL')

# Ensure critical environment variables are set
if not all([DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT]):
    raise ValueError("Missing required database environment variables")
if not API_URL or not WS_URL:
    raise ValueError("Missing Kraken API or WebSocket URLs")

# Load trading configuration from config.json
with open('config.json', 'r', encoding='utf-8') as config_file:
    config = json.load(config_file)

# Extract mode and parameters
ema_short = config["ema_short"]
ema_long = config["ema_long"]
mode = config['mode']
pair = config['pair']
currency = config["currency"]
initial_portfolio = config['initial_portfolio']
investment_percentage = config['investment_percentage']
max_investment = config['max_investment']
taker_maker_fee = config['taker_maker_fee']
take_profit_levels = config['take_profit_levels']
stop_loss = config['stop_loss']
min_investment = config['min_investment']
sell_at_target = config['sell_at_target']
position_size_multiplier = config['position_size_multiplier']
volatility_multiplier = config['volatility_multiplier']
interval = config['interval']
local = config['local']
use_simulator = config.get('use_simulator', False)
simulator_data_file = config.get('simulator_data_file', None)
min_body_ratio = config.get('min_body_ratio')
max_upper_wick_ratio = config.get('max_upper_wick_ratio')
min_volume_multiplier = config.get('min_volume_multiplier')
pullback_threshold = config.get('pullback_threshold')

if local:
    DB_HOST = 'localhost'

# Initialize with default values
start_date: Optional[datetime] = None
end_date: Optional[datetime] = None

logger.info(f"STARTING {mode} trading for pair {pair}")

# Parse dates for backtest mode
if mode == 'backtest':
    start_date = datetime.strptime(config['start_date'], '%Y-%m-%d')
    end_date = datetime.strptime(config['end_date'], '%Y-%m-%d')

# Initialize DatabaseExecutor
db = DatabaseExecutor(DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT)

# Initialize strategy
#strategy = EMAStrategy(
#    pair=pair,
#    short_window=ema_short,  # Could be added to config if needed
#    long_window=ema_long,
#    take_profit_levels=take_profit_levels,
#    stop_loss=stop_loss,
#    interval=interval,
#    investment_percentage=investment_percentage,
#    taker_maker_fee=taker_maker_fee,
#    max_investment=max_investment,
#    min_investment=min_investment,
#    min_body_ratio=min_body_ratio,
#    max_upper_wick_ratio=max_upper_wick_ratio,
#    min_volume_multiplier=min_volume_multiplier,
#    pullback_threshold=pullback_threshold
#)

strategy = PPOStrategy(
    pair=pair,
    short_window=ema_short,  # Could be added to config if needed
    long_window=ema_long,
    take_profit_levels=take_profit_levels,
    stop_loss=stop_loss,
    interval=interval,
    taker_maker_fee=taker_maker_fee,
    max_investment=max_investment,
    min_investment=min_investment,
    position_size_multiplier=position_size_multiplier,
    volatility_multiplier=volatility_multiplier
)


if not KRAKEN_API_KEY or not KRAKEN_API_SECRET:
    raise ValueError("Kraken API key and secret are required for live trading")

api = KrakenAPI(KRAKEN_API_KEY, KRAKEN_API_SECRET, API_URL, use_simulator, simulator_data_file, default_pair=pair)
# Initialize portfolio and trade managers
portfolio_manager = PortfolioManager(initial_portfolio, api, pair[0:3], currency)
trade_history = TradeHistory(mode=mode, db=db, logger=logger)  # Mode-specific trade logging

# Switch to select DataFeed and OrderExecutor based on mode
data_feed: DataFeed
order_executor: OrderExecutor

if mode == 'backtest':
    # Ensure start_date and end_date are not None for backtest mode
    if start_date is None or end_date is None:
        raise ValueError("start_date and end_date must be provided for backtest mode")
    data_feed = HistoricalDataFeed(db, pair, start_date, end_date, strategy.preprocess_data)
    order_executor = SimulatedOrderExecutor()
    trade_history.clear_previous_results()
elif mode == 'paper':
    data_feed = KrakenDataFeed(pair, interval, api, db, strategy)
    order_executor = SimulatedOrderExecutor()
    trade_history.clear_previous_results()
elif mode == 'live':
    data_feed = KrakenDataFeed(pair, interval, api, db, strategy)
    order_executor = RealOrderExecutor(api)
else:
    raise ValueError(f"Invalid mode: {mode}. Choose 'backtest', 'paper', or 'live'.")


trade_manager = TradeManager(portfolio_manager=portfolio_manager, order_executor=order_executor, trade_history=trade_history, api=api, mode=mode)

trader = Trader(
    strategy=strategy,
    data_feed=data_feed,
    order_executor=order_executor,
    trade_manager=trade_manager,
    portfolio_manager=portfolio_manager,
    mode=mode
)
async def main():
    try:
        if mode == 'live':
            trade_manager.sync_open_trades(db, take_profit_levels)
            await portfolio_manager.sync_with_exchange(trade_manager.active_trades)

        await trader.run()
    except Exception as e:
        logger.error(f"Fatal error in main execution: {e}", exc_info=True)
        # Optionally, perform cleanup or notify administrators
        raise  # Re-raise to ensure the program exits with an error code

try:
    asyncio.run(main())
except KeyboardInterrupt:
    logger.info("Trading stopped by user")
except Exception as e:
    logger.critical(f"Unhandled exception: {e}", exc_info=True)
    # Optionally, send notification or perform emergency cleanup

# Analyze results (for backtest mode only)
if mode == 'backtest':
    strategy_name = strategy.__class__.__name__
    analyzer = BacktestAnalyzer()
    # Check if data_feed is HistoricalDataFeed and has candles
    if isinstance(data_feed, HistoricalDataFeed) and hasattr(data_feed, 'candles') and data_feed.candles is not None:
        analyzer.analyze_results(db, strategy_name, initial_portfolio, pair)
    else:
        print("Warning: No candle data available for analysis")
