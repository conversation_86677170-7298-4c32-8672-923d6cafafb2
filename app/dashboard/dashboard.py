import json
import dash
from dash import dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objs as go
import dash_bootstrap_components as dbc
from data_fetcher import fetch_metrics, compute_historical_portfolio_value

# Load configuration
with open('config.json', 'r') as config_file:
    config = json.load(config_file)
mode = config['mode']
pair = config['pair']
currency = config['currency']
initial_portfolio = config['initial_portfolio']

# Initialize Dash app with a professional theme and Font Awesome
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.SANDSTONE, 'https://use.fontawesome.com/releases/v5.15.3/css/all.css'])

# Define metric names and labels
metric_names = [
    ('portfolio_value', 'Portfolio Value'),
    ('cash', 'Cash'),
    ('positions_value', 'Positions Value'),
    ('asset_balance', 'Asset Balance'),
    ('current_price', 'Current Price'),
    ('drawdown', 'Drawdown'),
    ('max_drawdown', 'Max Drawdown'),
    ('total_return', 'Total Return'),
    ('realized_pnl', 'Realized PnL'),
    ('unrealized_pnl', 'Unrealized PnL'),
    ('win_rate', 'Win Rate'),
    ('sharpe_ratio', 'Sharpe Ratio'),
    ('num_open_positions', 'Open Positions'),
    ('num_trades', 'Total Trades'),
    ('avg_trade_duration', 'Avg Trade Duration')
]

# Professional color palette
primary_color = '#2C3E50'  # Dark blue-gray
secondary_color = '#1ABC9C'  # Teal accent
accent_colors = [
    '#3498DB',  # Blue
    '#9B59B6',  # Purple
    '#2ECC71',  # Green
    '#F1C40F',  # Yellow
    '#E67E22',  # Orange
    '#E74C3C',  # Red
    '#1ABC9C',  # Teal
    '#34495E',  # Navy
    '#16A085',  # Dark teal
    '#27AE60',  # Emerald
    '#2980B9',  # Royal blue
    '#8E44AD',  # Violet
    '#F39C12'   # Amber
]

# Icon map for Font Awesome icons (unchanged)
icon_map = {
    'portfolio_value': 'fa-wallet',
    'cash': 'fa-money-bill',
    'positions_value': 'fa-chart-line',
    'asset_balance': 'fa-chart-pie',
    'current_price': 'fa-dollar-sign',
    'drawdown': 'fa-arrow-down',
    'max_drawdown': 'fa-exclamation-triangle',
    'total_return': 'fa-percentage',
    'realized_pnl': 'fa-hand-holding-usd',
    'unrealized_pnl': 'fa-clock',
    'win_rate': 'fa-trophy',
    'sharpe_ratio': 'fa-balance-scale',
    'num_open_positions': 'fa-folder-open',
    'num_trades': 'fa-exchange-alt',
    'avg_trade_duration': 'fa-hourglass-half'
}

# Custom CSS for professional styling
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>Trading Dashboard</title>
        {%favicon%}
        {%css%}
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            }
            .card {
                transition: transform 0.3s, box-shadow 0.3s;
            }
            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(120deg, ''' + primary_color + ''', ''' + secondary_color + ''');
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 30px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .metric-value {
                font-size: 1.5rem;
                font-weight: 600;
            }
            .metric-label {
                font-size: 0.9rem;
                color: #6c757d;
            }
            .chart-container {
                background-color: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# Define the layout
app.layout = dbc.Container([
    # Header with gradient background
    dbc.Row([
        dbc.Col(html.Div([
            html.H1(f"{mode.capitalize()} Trading Dashboard", className='mb-0'),
            html.H4(f"Pair: {pair}", className='text-light opacity-75')
        ], className='header text-center'), width=12)
    ]),

    # Metrics grid
    html.Div(id='metrics-display', className='mb-4'),

    # Chart section
    dbc.Row([
        dbc.Col(html.Div([
            html.H3('Portfolio Performance', className='mb-4'),
            dcc.Graph(id='portfolio-chart')
        ], className='chart-container'), width=12)
    ], className="mb-4"),

    # Interval component for updates
    dcc.Interval(id='interval-component', interval=60*1000, n_intervals=0)
], fluid=True, className='py-4')

# Define the callback
@app.callback(
    [Output('metrics-display', 'children'),
     Output('portfolio-chart', 'figure')],
    [Input('interval-component', 'n_intervals')]
)
def update_dashboard(_):  # Renamed parameter to underscore to indicate it's unused
    metrics = fetch_metrics(pair, mode, currency, initial_portfolio)

    # Create metrics grid
    metrics_cols = []
    for i, (key, name) in enumerate(metric_names):
        # Format the value based on metric type
        if key in ['drawdown', 'max_drawdown', 'total_return', 'win_rate']:
            formatted_value = f"{metrics[key]:.2f}%"
            # Add arrow icons for performance indicators
            if key == 'total_return':
                icon_class = 'fa-arrow-up text-success' if metrics[key] >= 0 else 'fa-arrow-down text-danger'
                value_class = 'text-success' if metrics[key] >= 0 else 'text-danger'
            else:
                icon_class = icon_map[key]
                value_class = ''
        elif key == 'avg_trade_duration':
            formatted_value = f"{metrics[key]:.2f} hours"
            icon_class = icon_map[key]
            value_class = ''
        elif key == 'asset_balance':
            formatted_value = f"{metrics[key]:.5f}"
            icon_class = icon_map[key]
            value_class = ''
        else:
            formatted_value = f"{metrics[key]:.2f}"
            icon_class = icon_map[key]
            value_class = ''

        # Create card for each metric
        metrics_cols.append(
            dbc.Col(
                dbc.Card([
                    html.Div([
                        html.I(className=f"fas {icon_class} fa-fw mr-2"),
                        html.Span(name, className='metric-label')
                    ], className='d-flex align-items-center mb-2'),
                    html.Div(
                        formatted_value,
                        className=f'metric-value {value_class}'
                    )
                ],
                body=True,
                style={
                    'borderTop': f'4px solid {accent_colors[i % len(accent_colors)]}',
                    'height': '100%'
                },
                className='card h-100'
                ),
                md=3,
                sm=6,
                className='mb-4'
            )
        )

    # Portfolio value chart with improved styling
    portfolio_history = compute_historical_portfolio_value(pair, mode, initial_portfolio)
    figure = {
        'data': [
            go.Scatter(
                x=portfolio_history['timestamp'],
                y=portfolio_history['portfolio_value'],
                mode='lines',
                fill='tozeroy',
                line={
                    'color': secondary_color,
                    'width': 2
                },
                fillcolor=f'rgba({int(secondary_color[1:3], 16)}, {int(secondary_color[3:5], 16)}, {int(secondary_color[5:7], 16)}, 0.1)'
            )
        ],
        'layout': go.Layout(
            xaxis={
                'title': 'Time',
                'gridcolor': '#f5f5f5',
                'zeroline': False
            },
            yaxis={
                'title': f'Value ({currency})',
                'gridcolor': '#f5f5f5',
                'zeroline': False
            },
            hovermode='closest',
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            margin={'l': 40, 'r': 40, 't': 20, 'b': 40},
            font={'family': 'Segoe UI, Roboto, Helvetica Neue, sans-serif'},
            legend={'orientation': 'h'},
            height=500
        )
    }

    return dbc.Row(metrics_cols), figure

# Run the app
if __name__ == '__main__':
    app.run_server(debug=True)