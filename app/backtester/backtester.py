import logging
from typing import List, Tuple, Optional
from app.trade_history.trade_action import TradeAction
import pandas as pd
from datetime import datetime
from app.strategy.base_strategy import BaseStrategy
from app.trade_manager.trade_manager import TradeManager
from app.trade_manager.portfolio_manager import PortfolioManager
from app.db.db_executor import DatabaseExecutor
from app.trade_manager.result import Result

logger = logging.getLogger(__name__)

class Backtester:
    def __init__(self, db: Optional[DatabaseExecutor], strategy: BaseStrategy, pair: str,
                 start_date: Optional[datetime], end_date: Optional[datetime], trade_manager: TradeManager,
                 portfolio_manager: PortfolioManager):
        self.db = db
        self.strategy = strategy
        self.pair = pair
        self.start_date = start_date
        self.end_date = end_date
        self.trade_manager = trade_manager
        self.portfolio_manager = portfolio_manager
        self.logger = logging.getLogger(__name__)

    def get_ohlc_data(self) -> pd.DataFrame:
        if not self.db or not self.start_date or not self.end_date:
            self.logger.warning("Database connection or date range not provided, cannot fetch OHLC data")
            return pd.DataFrame()

        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        results = self.db.execute_select(query, (self.pair, self.start_date, self.end_date),
                                         float_columns=[1, 2, 3, 4, 5])
        df = pd.DataFrame(results, columns=['timestamp', 'open_price', 'high_price',
                                            'low_price', 'close_price', 'volume'])
        df = self.strategy.preprocess_data(df)
        return df

    def clear_previous_results(self):
        self.logger.info("Clearing previous backtest results...")
        if self.db:
            delete_trades_query = "DELETE FROM backtest_trades"
            delete_results_query = "DELETE FROM backtest_results"
            self.db.execute_delete(delete_trades_query)
            self.db.execute_delete(delete_results_query)
            self.logger.info("Previous backtest results cleared.")
        else:
            self.logger.warning("No database connection, skipping clear_previous_results")

    def run_backtest(self, df: pd.DataFrame) -> Tuple[List[TradeAction], List[Result]]:
        #df = self.strategy.preprocess_data(df)
        trades = []
        results = []

        for i in range(1, len(df)):
            current_candle = df.iloc[i]
            previous_candle = df.iloc[i-1]
            minusthree_candle = df.iloc[i-2]
            self.strategy.process_candle(current_candle, previous_candle, minusthree_candle, self.trade_manager, self.portfolio_manager)

        # Close all active trades at the end of the backtest
        last_candle = df.iloc[-1]
        exit_price = last_candle["close_price"]
        trades = list(self.trade_manager.active_trades.values())
        for trade in trades:
            fee = round(exit_price * trade.remaining_volume * 0.0025, 2)
            self.trade_manager.complete_exit(trade.trade_id, exit_price, last_candle["timestamp"],
                      fee, 'end_of_backtest')

        trades = self.trade_manager.get_trade_actions()
        results = self.trade_manager.get_trade_results()

        return trades, results

    def save_results(self, trades: List[TradeAction], results: List[Result]):
        if not self.db:
            self.logger.warning("No database connection, skipping save_results")
            return

        try:
            with self.db.get_connection() as conn:
                with conn.cursor() as cursor:
                    trade_query = """
                    INSERT INTO backtest_trades (trade_id, trade_id_match, price, volume, value, timestamp, pair, buy_sell, fee, reason)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    for trade in trades:
                        trade_data = (
                            trade.trade_id,
                            trade.trade_id_match,
                            trade.price,
                            trade.volume,
                            trade.value,
                            trade.timestamp,
                            trade.pair,
                            trade.action_type,
                            trade.fee,
                            trade.reason
                        )
                        cursor.execute(trade_query, trade_data)

                    result_query = """
                    INSERT INTO backtest_results (strategy_name, entry_trade_id, exit_trade_id, entry_price, exit_price, entry_fee, exit_fee,
                                                  entry_timestamp, exit_timestamp, volume, profit_loss, duration, success, exit_reason)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    for result in results:
                        result_data = (
                            result.strategy_name,
                            result.entry_trade_id,
                            result.exit_trade_id,
                            result.entry_price,
                            result.exit_price,
                            result.entry_fee,
                            result.exit_fee,
                            result.entry_timestamp,
                            result.exit_timestamp,
                            result.volume,
                            result.profit_loss,
                            result.duration,
                            result.success,
                            result.exit_reason
                        )
                        cursor.execute(result_query, result_data)

                    conn.commit()
        except Exception as e:
            if self.db:
                try:
                    with self.db.get_connection() as conn:
                        conn.rollback()
                except:
                    pass
            self.logger.error(f"Error saving results: {str(e)}")
            raise e