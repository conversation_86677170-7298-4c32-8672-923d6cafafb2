# optimizer_optuna.py
import logging
import os
import sys
import traceback
import optuna
from dataclasses import dataclass
from datetime import datetime
from multiprocessing import cpu_count
import asyncio
import json

from os import getenv
from typing import Dict, Optional, Any  # List is unused
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

import pandas as pd
# numpy as np is unused
# from scipy.optimize import minimize is unused
from stable_baselines3 import PPO

from ksLib.environment import Environment
from app.backtester.backtestAnalyzer import BacktestAnalyzer
from app.db.db_executor import DatabaseExecutor
from app.backtester.backtester import Backtester
from app.strategy.ppo_strategy import PPOStrategy
from app.trade_manager.trade_manager import TradeManager
from app.trade_manager.portfolio_manager import PortfolioManager
from app.scripts.simulated_order_executor import SimulatedOrderExecutor
from app.trade_history.trade_history import TradeHistory
from app.strategy.trading_env import TradingEnv
from app.scripts.trader import Trader
from app.scripts.historical_data_feed import HistoricalDataFeed


# Setup logging
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """Data class to store optimization results"""
    parameters: Dict[str, Any]  # Can contain int, float, or other values
    profit: float
    success: bool
    message: str

class StrategyOptimizer:
    def __init__(
        self,
        db,
        pair: str,
        initial_portfolio: float,
        taker_maker_fee: float,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1h',
        n_workers: Optional[int] = None,
        min_investment: float = 200,
    ):
        self.db = db
        self.pair = pair
        self.initial_portfolio = initial_portfolio
        self.taker_maker_fee = taker_maker_fee
        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.n_workers = n_workers or max(1, cpu_count() - 1)
        self.min_investment = min_investment

        # Define extended parameter bounds
        self.param_bounds = {
            # Trading parameters
            'stop_loss': (-0.3, -0.01),
            'take_profit_level1': (0.05, 0.3),
            'take_profit_level2': (0.2, 0.5),
            'take_profit_level3': (0.5, 0.8),
            'max_investment': (2500, 10000),
            'min_investment': (200, 500),
            # Risk management
            'volatility_multiplier': (2.0, 5.0),
            'position_size_multiplier': (15.0, 25.0),
            # Reward weights
            #'return_weight': (1.0, 2.0),
            #'sharpe_weight': (0.3, 1.5),
            #'sortino_weight': (0.3, 1.5),
            #'drawdown_weight': (0.2, 1.0),
            #'trading_penalty_weight': (0.1, 0.5),
            #'market_align_weight': (0.5, 2.0),
            #'profit_take_weight': (0.5, 2.0),
            #'cash_manage_weight': (0.1, 0.5)
        }

        self.bounds = list(self.param_bounds.values())
        self.param_names = list(self.param_bounds.keys())

        # Split data into training and testing
        full_data = self.get_ohlc_data()
        self.train_data = full_data[full_data['timestamp'] <= '2025-01-01']
        self.test_data = full_data[full_data['timestamp'] > '2025-01-01']

    def get_ohlc_data(self) -> pd.DataFrame:
        """Fetch raw OHLC data from the database"""
        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        results = self.db.execute_select(query, (self.pair, self.start_date, self.end_date),
                                         float_columns=[1, 2, 3, 4, 5])
        df = pd.DataFrame(results, columns=['timestamp', 'open_price', 'high_price',
                                            'low_price', 'close_price', 'volume'])
        return df

    def optimize(self, n_trials: int = 50) -> Dict:
        """Run hyperparameter optimization using Optuna"""
        def objective(trial: optuna.Trial):
            try:
                param_dict = {
                    # Trading parameters
                    'stop_loss': trial.suggest_float('stop_loss', -0.3, -0.01),
                    'take_profit_level1': trial.suggest_float('take_profit_level1', 0.05, 0.3),
                    'take_profit_level2': trial.suggest_float('take_profit_level2', 0.2, 0.5),
                    'take_profit_level3': trial.suggest_float('take_profit_level3', 0.5, 0.8),
                    'max_investment': trial.suggest_float('max_investment', 2500, 10000),
                    'min_investment': trial.suggest_float('min_investment', 200, 500),
                    # Risk management
                    'volatility_multiplier': trial.suggest_float('volatility_multiplier', 2.0, 5.0),
                    'position_size_multiplier': trial.suggest_float('position_size_multiplier', 15.0, 25.0),
                    # Reward weights
                    #'return_weight': trial.suggest_float('return_weight', 0.5, 2.0),
                    #'sharpe_weight': trial.suggest_float('sharpe_weight', 0.3, 1.5),
                    #'sortino_weight': trial.suggest_float('sortino_weight', 0.3, 1.5),
                    #'drawdown_weight': trial.suggest_float('drawdown_weight', 0.2, 1.0),
                    #'trading_penalty_weight': trial.suggest_float('trading_penalty_weight', 0.1, 0.5),
                    #'market_align_weight': trial.suggest_float('market_align_weight', 0.5, 2.0),
                    #'profit_take_weight': trial.suggest_float('profit_take_weight', 0.5, 2.0),
                    #'cash_manage_weight': trial.suggest_float('cash_manage_weight', 0.1, 0.5)
                    'return_weight': trial.suggest_float('return_weight', 2.0, 2.0),
                    'sharpe_weight': trial.suggest_float('sharpe_weight', 0.8, 0.8),
                    'sortino_weight': trial.suggest_float('sortino_weight', 0.7, 0.7),
                    'drawdown_weight': trial.suggest_float('drawdown_weight', 0.38, 0.38),
                    'trading_penalty_weight': trial.suggest_float('trading_penalty_weight', 0.1, 0.5),
                    'market_align_weight': trial.suggest_float('market_align_weight', 0.5, 0.5),
                    'profit_take_weight': trial.suggest_float('profit_take_weight', 1.75, 1.75),
                    'cash_manage_weight': trial.suggest_float('cash_manage_weight', 0.37, 0.37)
                }

                # Add PPO hyperparameters
                ppo_params = {
                    #'learning_rate': trial.suggest_float("learning_rate", 1e-5, 5e-4, log=True),
                    #'n_steps': trial.suggest_int("n_steps", 1024, 4096, step=256),
                    #'batch_size': trial.suggest_categorical("batch_size", [64, 96, 128, 256]),
                    #'n_epochs': trial.suggest_int("n_epochs", 5, 15),
                    #'gamma': trial.suggest_float("gamma", 0.95, 0.9999),
                    #'gae_lambda': trial.suggest_float("gae_lambda", 0.9, 0.98),
                    #'clip_range': trial.suggest_float("clip_range", 0.1, 0.3),
                    #'ent_coef': trial.suggest_float("ent_coef", 0.0, 0.1),
                    #'vf_coef': trial.suggest_float("vf_coef", 0.4, 0.8),
                    #'max_grad_norm': trial.suggest_float("max_grad_norm", 0.3, 1.0)
                    'learning_rate': trial.suggest_float("learning_rate",0.0003, 0.0003),
                    'n_steps': trial.suggest_int("n_steps",2048, 2048),
                    'batch_size': trial.suggest_categorical("batch_size", [64]),
                    'n_epochs': trial.suggest_int("n_epochs", 10, 10),
                    'gamma': trial.suggest_float("gamma", 0.99, 0.99),
                    'gae_lambda': trial.suggest_float("gae_lambda", 0.95, 0.95),
                    'clip_range': trial.suggest_float("clip_range", 0.2, 0.2),
                    'ent_coef': trial.suggest_float("ent_coef", 0.01, 0.01),
                    'vf_coef': trial.suggest_float("vf_coef", 0.5, 0.5),
                    'max_grad_norm': trial.suggest_float("max_grad_norm", 0.5, 0.5)
                }

                take_profit_levels = [
                    param_dict['take_profit_level1'],
                    param_dict['take_profit_level2'],
                    param_dict['take_profit_level3']
                ]

                reward_weights = {
                    'return_component': param_dict['return_weight'],
                    'sharpe_component': param_dict['sharpe_weight'],
                    'sortino_component': param_dict['sortino_weight'],
                    'drawdown_penalty': param_dict['drawdown_weight'],
                    'trading_penalty': param_dict['trading_penalty_weight'],
                    'market_alignment': param_dict['market_align_weight'],
                    'profit_taking': param_dict['profit_take_weight'],
                    'cash_management': param_dict['cash_manage_weight']
                }

                strategy = PPOStrategy(
                    pair=self.pair,
                    stop_loss=param_dict['stop_loss'],
                    take_profit_levels=take_profit_levels,
                    max_investment=param_dict['max_investment'],
                    min_investment=param_dict['min_investment'],
                    volatility_multiplier=param_dict['volatility_multiplier'],
                    position_size_multiplier=param_dict['position_size_multiplier']
                )

                # Train on pre-2025 data
                preprocessed_train = strategy.preprocess_data(self.train_data.copy())
                train_env = TradingEnv(preprocessed_train, strategy, reward_weights=reward_weights)
                strategy.model = PPO(
                    "MlpPolicy",
                    train_env,
                    learning_rate=ppo_params['learning_rate'],
                    n_steps=ppo_params['n_steps'],
                    batch_size=ppo_params['batch_size'],
                    n_epochs=ppo_params['n_epochs'],
                    gamma=ppo_params['gamma'],
                    gae_lambda=ppo_params['gae_lambda'],
                    clip_range=ppo_params['clip_range'],
                    ent_coef=ppo_params['ent_coef'],
                    vf_coef=ppo_params['vf_coef'],
                    max_grad_norm=ppo_params['max_grad_norm'],
                    verbose=0
                )
                strategy.model.learn(total_timesteps=50000)

                # Validate on post-2025 data
                portfolio_manager = PortfolioManager(
                    self.initial_portfolio, None, self.pair[0:3], self.pair[3:6]
                )
                trade_history = TradeHistory(mode="backtest", db=None, logger=logger)
                order_executor = SimulatedOrderExecutor()
                trade_manager = TradeManager(portfolio_manager, order_executor, trade_history, api=None, mode="backtest")

                backtester = Backtester(
                    db=None,
                    strategy=strategy,
                    pair=self.pair,
                    start_date=None,
                    end_date=None,
                    trade_manager=trade_manager,
                    portfolio_manager=portfolio_manager
                )

                preprocessed_test = strategy.preprocess_data(self.test_data.copy())
                _, results = backtester.run_backtest(preprocessed_test)  # Ignore trades, only need results

                if not results:
                    raise optuna.TrialPruned()

                df = pd.DataFrame(results)
                final_profit = df['profit_loss'].sum()
                # Add the PPO parameters to the returned result
                param_dict.update(ppo_params)
                return final_profit

            except Exception as e:
                logger.error(f"Trial failed: {e}")
                raise optuna.TrialPruned()

        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials, n_jobs=self.n_workers)

        best = study.best_trial

        optimal_params = {
            'parameters': {
                **best.params,
                'take_profit_levels': [
                    best.params['take_profit_level1'],
                    best.params['take_profit_level2'],
                    best.params['take_profit_level3']
                ],
                'reward_weights': {
                    'return_component': best.params['return_weight'],
                    'sharpe_component': best.params['sharpe_weight'],
                    'sortino_component': best.params['sortino_weight'],
                    'drawdown_penalty': best.params['drawdown_weight'],
                    'trading_penalty': best.params['trading_penalty_weight'],
                    'market_alignment': best.params['market_align_weight'],
                    'profit_taking': best.params['profit_take_weight'],
                    'cash_management': best.params['cash_manage_weight']
                }
            },
            'expected_profit': best.value,
            'success': True,
            'message': f"Best trial completed successfully with profit: {best.value}",
            'n_successful_optimizations': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        }

        return optimal_params

    def train_and_save_model(self, optimal_params):
        """Train the model with optimized parameters and save it"""
        logger.info("Training model with optimized parameters...")

        # Initialize strategy with optimized parameters
        strategy = PPOStrategy(
            pair=self.pair,
            stop_loss=optimal_params['parameters']['stop_loss'],
            take_profit_levels=optimal_params['parameters']['take_profit_levels'],
            max_investment=optimal_params['parameters']['max_investment'],
            min_investment=optimal_params['parameters']['min_investment'],
            volatility_multiplier=optimal_params['parameters']['volatility_multiplier'],
            position_size_multiplier=optimal_params['parameters']['position_size_multiplier']
        )

        # Generate a timestamp and model name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_name = f"ppo_model_{self.pair}_{timestamp}"
        model_path = f"models/{model_name}"

        # Create directory if it doesn't exist
        os.makedirs("models", exist_ok=True)

        # Train model on the training data with optimized reward weights
        preprocessed_train = strategy.preprocess_data(self.train_data.copy())
        train_env = TradingEnv(
            preprocessed_train,
            strategy,
            reward_weights=optimal_params['parameters']['reward_weights']
        )

        # Create and train the model with optimized PPO parameters
        strategy.model = PPO(
            "MlpPolicy",
            train_env,
            learning_rate=optimal_params['parameters']['learning_rate'],
            n_steps=optimal_params['parameters']['n_steps'],
            batch_size=optimal_params['parameters']['batch_size'],
            n_epochs=optimal_params['parameters']['n_epochs'],
            gamma=optimal_params['parameters']['gamma'],
            gae_lambda=optimal_params['parameters']['gae_lambda'],
            clip_range=optimal_params['parameters']['clip_range'],
            ent_coef=optimal_params['parameters']['ent_coef'],
            vf_coef=optimal_params['parameters']['vf_coef'],
            max_grad_norm=optimal_params['parameters']['max_grad_norm'],
            verbose=1
        )
        strategy.model.learn(total_timesteps=50000)

        # Save the model
        strategy.model.save(model_path)
        logger.info(f"Model saved to {model_path}")

        return model_path

    async def run_full_backtest(self, optimal_params):
        """Run a complete backtest using the optimized parameters in the main.py way"""
        logger.info("Starting full backtest with optimized parameters...")
        mode = "backtest"
        # Use the test data date range for historical data feed
        std = self.test_data['timestamp'].min() if not self.test_data.empty else self.start_date
        edt = self.test_data['timestamp'].max() if not self.test_data.empty else self.end_date

        # Initialize strategy with optimized parameters
        strategy = PPOStrategy(
            pair=self.pair,
            stop_loss=optimal_params['parameters']['stop_loss'],
            take_profit_levels=optimal_params['parameters']['take_profit_levels'],
            max_investment=optimal_params['parameters']['max_investment'],
            min_investment=optimal_params['parameters']['min_investment'],
            volatility_multiplier=optimal_params['parameters']['volatility_multiplier'],
            position_size_multiplier=optimal_params['parameters']['position_size_multiplier']
        )

        # Train model on the training data with optimized reward weights
        preprocessed_train = strategy.preprocess_data(self.train_data.copy())
        train_env = TradingEnv(
            preprocessed_train,
            strategy,
            reward_weights=optimal_params['parameters']['reward_weights']
        )
        strategy.model = PPO(
            "MlpPolicy",
            train_env,
            learning_rate=optimal_params['parameters']['learning_rate'],
            n_steps=optimal_params['parameters']['n_steps'],
            batch_size=optimal_params['parameters']['batch_size'],
            n_epochs=optimal_params['parameters']['n_epochs'],
            gamma=optimal_params['parameters']['gamma'],
            gae_lambda=optimal_params['parameters']['gae_lambda'],
            clip_range=optimal_params['parameters']['clip_range'],
            ent_coef=optimal_params['parameters']['ent_coef'],
            vf_coef=optimal_params['parameters']['vf_coef'],
            max_grad_norm=optimal_params['parameters']['max_grad_norm'],
            verbose=1
        )
        strategy.model.learn(total_timesteps=50000)

        # Initialize components for backtesting (like in main.py)
        portfolio_manager = PortfolioManager(
            self.initial_portfolio, None, self.pair[0:3], self.pair[3:6]
        )
        trade_history = TradeHistory(mode=mode, db=self.db, logger=logger)
        trade_history.clear_previous_results()
        order_executor = SimulatedOrderExecutor()

        # Initialize trade manager
        trade_manager = TradeManager(
            portfolio_manager=portfolio_manager,
            order_executor=order_executor,
            trade_history=trade_history,
            api=None,
            mode=mode
        )

        # Set up historical data feed with the test data
        data_feed = HistoricalDataFeed(
            self.db,
            self.pair,
            std,
            edt,
            strategy.preprocess_data
        )
        # Use the test data directly instead of fetching from DB
        preprocessed_test = strategy.preprocess_data(self.test_data.copy())
        data_feed.candles = preprocessed_test

        # Initialize the trader (like in main.py)
        trader = Trader(
            strategy=strategy,
            data_feed=data_feed,
            order_executor=order_executor,
            trade_manager=trade_manager,
            portfolio_manager=portfolio_manager,
            mode=mode
        )

        # Run the backtest
        await trader.run()

        # Analyze results
        strategy_name = strategy.__class__.__name__
        analyzer = BacktestAnalyzer()
        analyzer.analyze_results(self.db, strategy_name, self.initial_portfolio, self.pair)

        # Get final portfolio value
        final_portfolio = portfolio_manager.current_balance
        profit = final_portfolio - self.initial_portfolio
        roi = profit / self.initial_portfolio * 100

        logger.info(f"Backtest completed - Final portfolio: ${final_portfolio:.2f}, Profit: ${profit:.2f}, ROI: {roi:.2f}%")

        return {
            'final_portfolio': final_portfolio,
            'profit': profit,
            'roi': roi,
            'backtest_completed': True
        }


if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    try:
        e = Environment()
        e.load_generic_environment_files()
        env = getenv("ENVIRONMENT")
        mode = "backtest"

        db = DatabaseExecutor(
            db_name=getenv('DB_NAME'),
            db_user=getenv('DB_USER'),
            db_password=getenv('DB_PASSWORD'),
            db_host='localhost',
            db_port=getenv('DB_PORT')
        )

        params = {
            'pair': 'SOLUSD',
            'initial_portfolio': 5000,
            'taker_maker_fee': 0.0026,
            'min_investment': 100,
            'interval': '1h'
        }

        start_date = datetime(2021, 6, 18)
        start_date_opt = datetime(2025, 1, 1)
        end_date = datetime(2025, 4, 25)
########### NUMBER OF OPTIMIZATIONS ##########################################
        opt_number = 50

        optimizer = StrategyOptimizer(
            db=db,
            pair=params['pair'],
            initial_portfolio=params['initial_portfolio'],
            taker_maker_fee=params['taker_maker_fee'],
            start_date=start_date,
            end_date=end_date,
            interval=params['interval'],
            n_workers=8,
            min_investment=params['min_investment']
        )

        logger.info("Starting optimization process...")
        optimal_params = optimizer.optimize(n_trials=opt_number)

        if not optimal_params or not optimal_params.get('success'):
            logger.error("Optimization failed to find optimal parameters")
            sys.exit(1)

        print("\nOptimization Results:")
        print("-" * 50)
        print("Optimal Parameters:")
        for param_name, param_value in optimal_params['parameters'].items():
            if isinstance(param_value, list):
                print(f"{param_name}:")
                for i, v in enumerate(param_value, 1):
                    print(f"  Level {i}: {v:.4f}")
            elif isinstance(param_value, dict):
                print(f"{param_name}:")
                for k, v in param_value.items():
                    print(f"  {k}: {v:.4f}")
            else:
                print(f"{param_name}: {param_value:.4f}")

        print(f"\nExpected Profit: ${optimal_params['expected_profit']:.2f}")
        print(f"Successful Optimizations: {optimal_params['n_successful_optimizations']}")
        print("-" * 50)



        try:


            # First train and save the model with optimized parameters
            print("\nTraining and saving model with optimized parameters...")
            model_path = optimizer.train_and_save_model(optimal_params)
            print(f"Model saved to: {model_path}")


            # Run the full backtest with optimized parameters
            backtest_results = asyncio.run(optimizer.run_full_backtest(optimal_params))

            print("\nFinal Backtest Results:")
            print("-" * 50)
            print(f"Final Portfolio Value: ${backtest_results['final_portfolio']:.2f}")
            print(f"Total Profit: ${backtest_results['profit']:.2f}")
            print(f"Return on Investment: {backtest_results['roi']:.2f}%")
            print("-" * 50)

            # Save the optimized parameters and backtest results to a file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_filename = f"models/optimization_results_{params['pair']}_{timestamp}.json"

            with open(results_filename, 'w') as f:
                json.dump({
                    'parameters': optimal_params['parameters'],
                    'optimization_results': {
                        'expected_profit': optimal_params['expected_profit'],
                        'successful_trials': optimal_params['n_successful_optimizations']
                    },
                    'backtest_results': backtest_results
                }, f, indent=4, default=str)

            print(f"Results saved to {results_filename}")

        except Exception as e:
            logger.error(f"Final backtest failed: {str(e)}")
            logger.error(traceback.format_exc())
            optimal_params['final_backtest_error'] = str(e)

        logger.info(f"Optimization completed: {optimal_params}")

    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        logger.error(traceback.format_exc())
        raise
