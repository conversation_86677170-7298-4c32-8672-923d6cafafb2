import sys
import os
import requests
import time
import urllib.parse
import hashlib
import hmac
import base64
import threading
import logging
from typing import Dict, Any, List, Optional
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.append(project_root)

logger = logging.getLogger(__name__)
class KrakenAPI:
    def __init__(self, api_key: str, api_secret: str, api_url: str, use_simulator: bool = False, simulator_data_file: Optional[str] = None, default_pair: str = 'XBTUSD'):
        self.api_key = api_key
        self.api_secret = api_secret
        self.api_url = api_url
        self.use_simulator = use_simulator
        self.default_pair = default_pair
        self.last_nonce = int(1000000 * time.time())
        self.nonce_lock = threading.Lock()

        # Initialize simulator if needed
        if use_simulator:
            from app.exchange.kraken_simulator import KrakenSimulator
            self.simulator = KrakenSimulator(simulator_data_file)

    def _get_nonce(self):
        with self.nonce_lock:
            nonce = int(1000000 * time.time())
            if nonce <= self.last_nonce:
                nonce = self.last_nonce + 1
            self.last_nonce = nonce
            return str(nonce)

    def _get_kraken_signature(self, urlpath: str, data: Dict[str, Any], secret: str) -> str:
        postdata = urllib.parse.urlencode(data)
        encoded = (data['nonce'] + postdata).encode()
        message = urlpath.encode() + hashlib.sha256(encoded).digest()
        mac = hmac.new(base64.b64decode(secret), message, hashlib.sha512)
        sigdigest = base64.b64encode(mac.digest())
        return sigdigest.decode()

    def _kraken_request(self, uri_path: str, data: Dict[str, Any], auth: bool = False) -> Dict[str, Any]:
        # If using simulator for authenticated endpoints
        if self.use_simulator and auth:
            ret = self.simulator.handle_request(uri_path, data)
            if ret['error'] != "Simulator: Endpoint not implemented":
                return ret

        # Otherwise, use the real API
        if auth:
            data['nonce'] = self._get_nonce()
            headers = {
                'API-Key': self.api_key,
                'API-Sign': self._get_kraken_signature(uri_path, data, self.api_secret)
            }
        else:
            headers = {}
        try:
            response = requests.post(f"{self.api_url}{uri_path}", headers=headers, data=data)
            result = response.json()

            if result.get("error"):
                logger.error(f"API returned error: {result['error']} | Endpoint: {uri_path} | Data: {data}")

            return result

        except ValueError as e:
            logger.exception(f"Failed to parse JSON from API response: {e}")
            raise
        except requests.RequestException as e:
            logger.exception(f"Request failed: {e}")
            raise

    # Public Endpoints

    def get_server_time(self) -> Dict[str, Any]:
        return self._kraken_request('/0/public/Time', {})

    def get_system_status(self) -> Dict[str, Any]:
        return self._kraken_request('/0/public/SystemStatus', {})

    # Removed duplicate methods that are redefined below with more complete implementations

    def get_ohlc_data(self, pair: str, interval: int = 1, since: int = int(time.time() - 86400) * 1000000000) -> Dict[str, Any]:
        return self._kraken_request('/0/public/OHLC', {'pair': pair, 'interval': interval, 'since': since})

    # Removed duplicate methods that are redefined below with more complete implementations

    def get_assets(self, asset: Optional[str] = None, aclass: Optional[str] = None) -> Dict[str, Any]:
        """Get information about the assets that are available for deposit, withdrawal, trading and staking."""
        params = {}
        if asset:
            params['asset'] = asset
        if aclass:
            params['aclass'] = aclass
        return self._kraken_request('/0/public/Assets', params)

    def get_asset_pairs(self, pair: Optional[str] = None, info: Optional[str] = None) -> Dict[str, Any]:
        """Get tradable asset pairs."""
        params = {}
        if pair:
            params['pair'] = pair
        if info:
            params['info'] = info
        return self._kraken_request('/0/public/AssetPairs', params)

    def get_ticker(self, pair: str) -> Dict[str, Any]:
        """Get ticker information."""
        return self._kraken_request('/0/public/Ticker', {'pair': pair})

    def get_ohlc(self, pair: str, interval: Optional[int] = None, since: Optional[int] = None) -> Dict[str, Any]:
        """Get OHLC data."""
        params = {'pair': pair}
        if interval:
            params['interval'] = interval
        if since:
            params['since'] = since
        return self._kraken_request('/0/public/OHLC', params)

    def get_order_book(self, pair: str, count: Optional[int] = None) -> Dict[str, Any]:
        """Get order book."""
        params = {'pair': pair}
        if count:
            params['count'] = count
        return self._kraken_request('/0/public/Depth', params)

    def get_recent_trades(self, pair: str, since: Optional[int] = None, count: Optional[int] = None) -> Dict[str, Any]:
        """Get recent trades."""
        params = {'pair': pair}
        if since:
            params['since'] = since
        if count:
            params['count'] = count
        return self._kraken_request('/0/public/Trades', params)

    def get_recent_spreads(self, pair: str, since: Optional[int] = None) -> Dict[str, Any]:
        """Get recent spreads."""
        params = {'pair': pair}
        if since:
            params['since'] = since
        return self._kraken_request('/0/public/Spread', params)

    def get_tradable_asset_pairs_price_tickers(self, pair: Optional[List[str]] = None) -> Dict[str, Any]:
        """Get tradable asset pair prices."""
        params = {}
        if pair:
            params['pair'] = ','.join(pair)
        return self._kraken_request('/0/public/Ticker', params)

    def get_tradable_asset_pairs_ohlc_data(self, pair: str, interval: Optional[int] = None, since: Optional[int] = None) -> Dict[str, Any]:
        """Get tradable asset pair OHLC data."""
        params = {'pair': pair}
        if interval:
            params['interval'] = interval
        if since:
            params['since'] = since
        return self._kraken_request('/0/public/OHLC', params)

    def get_tradable_asset_pairs_order_book(self, pair: str, count: Optional[int] = None) -> Dict[str, Any]:
        """Get tradable asset pair order book."""
        params = {'pair': pair}
        if count:
            params['count'] = count
        return self._kraken_request('/0/public/Depth', params)

    def get_tradable_asset_pairs_last_trade_data(self, pair: str, since: Optional[int] = None, count: Optional[int] = None) -> Dict[str, Any]:
        """Get tradable asset pair last trade data."""
        params = {'pair': pair}
        if since:
            params['since'] = since
        if count:
            params['count'] = count
        return self._kraken_request('/0/public/Trades', params)

    def get_tradable_asset_pairs_spread_data(self, pair: str, since: Optional[int] = None) -> Dict[str, Any]:
        """Get tradable asset pair spread data."""
        params = {'pair': pair}
        if since:
            params['since'] = since
        return self._kraken_request('/0/public/Spread', params)

    # Private Endpoints

    def get_account_balance(self) -> Dict[str, Any]:
        return self._kraken_request('/0/private/Balance', {}, auth=True)

    def get_trade_balance(self, asset: Optional[str] = None) -> Dict[str, Any]:
        data = {'asset': asset} if asset else {}
        return self._kraken_request('/0/private/TradeBalance', data, auth=True)

    def get_open_orders(self, trades: bool = False, userref: Optional[int] = None) -> Dict[str, Any]:
        data = {'trades': trades}
        if userref:
            data['userref'] = userref
        return self._kraken_request('/0/private/OpenOrders', data, auth=True)

    def get_closed_orders(self, trades: bool = False, userref: Optional[int] = None, start: Optional[str] = None, end: Optional[str] = None, ofs: Optional[str] = None, closetime: str = 'both') -> Dict[str, Any]:
        data = {'trades': trades, 'closetime': closetime}
        if userref:
            data['userref'] = userref
        if start:
            data['start'] = start
        if end:
            data['end'] = end
        if ofs:
            data['ofs'] = ofs
        return self._kraken_request('/0/private/ClosedOrders', data, auth=True)

    def query_orders_info(self, txid: str, trades: bool = False, userref: Optional[int] = None) -> Dict[str, Any]:
        data = {'txid': txid, 'trades': trades}
        if userref:
            data['userref'] = userref
        return self._kraken_request('/0/private/QueryOrders', data, auth=True)

    def get_trades_history(self, type: str = 'all', trades: bool = False, start: Optional[str] = None, end: Optional[str] = None, ofs: Optional[str] = None) -> Dict[str, Any]:
        data = {'type': type, 'trades': trades}
        if start:
            data['start'] = start
        if end:
            data['end'] = end
        if ofs:
            data['ofs'] = ofs
        return self._kraken_request('/0/private/TradesHistory', data, auth=True)

    def query_trades_info(self, txid: str, trades: bool = False) -> Dict[str, Any]:
        return self._kraken_request('/0/private/QueryTrades', {'txid': txid, 'trades': trades}, auth=True)

    def get_open_positions(self, txid: Optional[str] = None, docalcs: bool = False, consolidation: str = 'market') -> Dict[str, Any]:
        data = {'docalcs': docalcs, 'consolidation': consolidation}
        if txid:
            data['txid'] = txid
        return self._kraken_request('/0/private/OpenPositions', data, auth=True)

    def get_ledgers_info(self, asset: Optional[str] = None, aclass: str = 'currency', type: str = 'all', start: Optional[str] = None, end: Optional[str] = None, ofs: Optional[str] = None) -> Dict[str, Any]:
        data = {'aclass': aclass, 'type': type}
        if asset:
            data['asset'] = asset
        if start:
            data['start'] = start
        if end:
            data['end'] = end
        if ofs:
            data['ofs'] = ofs
        return self._kraken_request('/0/private/Ledgers', data, auth=True)

    def query_ledgers(self, id: str) -> Dict[str, Any]:
        return self._kraken_request('/0/private/QueryLedgers', {'id': id}, auth=True)

    def get_trade_volume(self, pair: Optional[str] = None, fee_info: bool = True) -> Dict[str, Any]:
        data = {'fee-info': fee_info}
        if pair:
            data['pair'] = pair
        return self._kraken_request('/0/private/TradeVolume', data, auth=True)

    def add_order(self, pair: str, type: str, ordertype: str, volume: str, price: Optional[str] = None, price2: Optional[str] = None, leverage: Optional[str] = None, oflags: Optional[str] = None, starttm: Optional[str] = None, expiretm: Optional[str] = None, userref: Optional[int] = None, validate: Optional[bool] = None, close_order_type: Optional[str] = None, close_price: Optional[str] = None, close_price2: Optional[str] = None, trading_agreement: Optional[str] = None) -> Dict[str, Any]:
        data = {
            'pair': pair,
            'type': type,
            'ordertype': ordertype,
            'volume': volume
        }
        if price: data['price'] = price
        if price2: data['price2'] = price2
        if leverage: data['leverage'] = leverage
        if oflags: data['oflags'] = oflags
        if starttm: data['starttm'] = starttm
        if expiretm: data['expiretm'] = expiretm
        if userref: data['userref'] = userref
        if validate: data['validate'] = validate
        if close_order_type: data['close[ordertype]'] = close_order_type
        if close_price: data['close[price]'] = close_price
        if close_price2: data['close[price2]'] = close_price2
        if trading_agreement: data['trading_agreement'] = trading_agreement
        return self._kraken_request('/0/private/AddOrder', data, auth=True)

    def cancel_order(self, txid: str) -> Dict[str, Any]:
        return self._kraken_request('/0/private/CancelOrder', {'txid': txid}, auth=True)

    def cancel_all_orders(self) -> Dict[str, Any]:
        return self._kraken_request('/0/private/CancelAll', {}, auth=True)

    def cancel_all_orders_after_x(self, timeout: int) -> Dict[str, Any]:
        return self._kraken_request('/0/private/CancelAllOrdersAfter', {'timeout': timeout}, auth=True)