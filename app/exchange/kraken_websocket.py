import websockets
import logging
import json
import asyncio
from datetime import datetime, timezone
from typing import Callable, Dict, Any, List, Union, Set

logger = logging.getLogger(__name__)

INTERVAL_MAPPING = {
    "min": 1,
    "h": 60,
    "D": 1440,    # 24 * 60
    "W": 10080,   # 7 * 1440
    "M": 43200,   # 30 * 1440 (approximate month)
}

class KrakenWebSocket:
    def __init__(self, ws_url: str = "wss://ws.kraken.com/v2"):
        self.ws_url = ws_url
        self.ws = None
        self.subscriptions = {}
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5  # Max attempts before giving up
        self.connection_listeners: Set['WebSocketConnectionListener'] = set()  # Store listeners for connection events
        self.message_task = None
        self.running = False
        self._reconnect_lock = asyncio.Lock()
        self._message_lock = asyncio.Lock()

    async def connect(self):
        # Use a lock to prevent multiple simultaneous connection attempts
        if self.ws and self.is_connected():
            logger.debug("WebSocket already connected")
            return

        # Cancel existing message task if any and wait for it to complete
        if self.message_task and not self.message_task.done():
            # Set a flag that tells handle_messages to exit its loop
            self.running = False

            # Close existing websocket to force handle_messages to exit
            if self.ws:
                await self.ws.close()
                self.ws = None

            # Cancel the task
            self.message_task.cancel()

            # Wait for a short time to allow the task to clean up
            try:
                await asyncio.wait_for(asyncio.shield(self.message_task), timeout=1.0)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                # Task didn't finish in time or was cancelled, which is fine
                pass
            except Exception as e:
                logger.warning(f"Error while waiting for message task to cancel: {e}")

            # Reset the task reference
            self.message_task = None

        self.subscriptions = {}

        # Now establish the new connection
        try:
            self.ws = await websockets.connect(self.ws_url)
            logger.info("WebSocket connection established")
            self.reconnect_attempts = 0

            # Reset the running flag and start a new message task
            if not self.running:
                self.running = True
                self.message_task = asyncio.create_task(self.handle_messages())

            await self._notify_connection_listeners(True)
            await self._resubscribe()

        except Exception as e:
            logger.error(f"Failed to establish WebSocket connection: {e}")
            self.reconnect_attempts += 1
            await self._notify_connection_listeners(False)
            raise

    def is_connected(self):
        """Check if the websocket is connected by examining its state"""
        if not self.ws:
            return False
        try:
            # Different versions of websockets library have different ways to check connection status
            # Try different attributes that might exist
            if hasattr(self.ws, 'closed'):
                return not self.ws.closed
            elif hasattr(self.ws, 'open'):
                return self.ws.open
            elif hasattr(self.ws, 'state') and hasattr(self.ws.state, 'value'):
                # Some websockets libraries use a state enum
                return self.ws.state.value == 1  # OPEN state is typically 1
            else:
                # If we can't determine, assume it's not connected to force a reconnection attempt
                return False
        except Exception:
            return False

    async def _resubscribe(self):
        """Resubscribe to all active subscriptions after reconnection"""
        if not self.subscriptions:
            return

        # Group subscriptions by channel type and symbols
        channel_groups = {}
        for channel_name, callback in self.subscriptions.items():
            parts = channel_name.split('-')
            if parts[0] == 'ohlc':
                channel_type = 'ohlc'
                interval = parts[1]
                symbol = parts[2]
                key = (channel_type, interval)
            else:
                channel_type = parts[0]
                symbol = parts[-1]
                key = (channel_type,)

            if key not in channel_groups:
                channel_groups[key] = {'symbols': set(), 'callback': callback}
            channel_groups[key]['symbols'].add(symbol)

        # Resubscribe to each group
        for key, details in channel_groups.items():
            channel_type = key[0]
            symbols = list(details['symbols'])
            callback = details['callback']

            if channel_type == 'ohlc':
                interval = key[1]
                await self.subscribe_ohlc(symbols, int(interval), callback)
            elif channel_type == 'book':
                depth = int(key[1]) if len(key) > 1 else 10
                await self.subscribe_book(symbols, depth, callback)
            elif channel_type == 'trade':
                await self.subscribe_trade(symbols, callback)
            elif channel_type == 'ticker':
                await self.subscribe_ticker(symbols, callback)
            elif channel_type == 'spread':
                await self.subscribe_spread(symbols, callback)

    async def reconnect(self):
        async with self._reconnect_lock:
            await self.disconnect()  # Clean up before reconnecting
            self.reconnect_attempts += 1
            if self.reconnect_attempts <= self.max_reconnect_attempts:
                logger.info(f"Reconnect attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}")
                await asyncio.sleep(2 ** self.reconnect_attempts)
                await self.connect()
            else:
                logger.error("Max reconnect attempts reached. Exiting.")
                self.running = False
                await self._notify_connection_listeners(False)

    async def _notify_connection_listeners(self, is_connected: bool):
        """Notify all registered listeners about connection state changes"""
        for listener in self.connection_listeners:
            try:
                if is_connected:
                    await listener.on_websocket_connected()
                else:
                    await listener.on_websocket_disconnected()
            except Exception as e:
                logger.error(f"Error notifying connection listener: {e}")

    def register_connection_listener(self, listener: 'WebSocketConnectionListener'):
        """Register a listener for connection state changes"""
        self.connection_listeners.add(listener)
        logger.info(f"Registered connection listener: {listener.__class__.__name__}")

    def unregister_connection_listener(self, listener: 'WebSocketConnectionListener'):
        """Unregister a connection state listener"""
        if listener in self.connection_listeners:
            self.connection_listeners.remove(listener)
            logger.info(f"Unregistered connection listener: {listener.__class__.__name__}")

    async def handle_messages(self):
        async with self._message_lock:
            try:
                while self.running:
                    try:
                        if not self.ws or not self.is_connected():
                            logger.warning("WebSocket connection lost during message handling")
                            # Don't reconnect here - exit the loop and let the reconnect logic handle it
                            return

                        message = await self.ws.recv()
                        data = json.loads(message)

                        if 'method' in data and data['method'] == 'subscribe':
                            # Handle subscription response
                            if 'result' in data:
                                result = data['result']
                                channel = result.get('channel', '')
                                symbol = result.get('symbol', '')
                                if isinstance(symbol, list):
                                    symbol = symbol[0]
                                if data.get('success', False):
                                    logger.info(f"Subscribed to {channel} for {symbol}")
                                else:
                                    logger.error(f"Subscription error: {data.get('errorMessage', 'Unknown error')}")
                            else:
                                logger.warning(f"Unknown subscribe message: {data}")
                        elif 'channel' in data:
                            channel = data['channel']
                            if channel == 'status':
                                # Handle status message
                                logger.info(f"Received status update: {data['data']}")
                            elif channel == 'heartbeat':
                                # Handle heartbeat message
                                pass
                            elif channel == 'ohlc' and data['type'] == 'snapshot':
                                pass
                            elif channel == 'ohlc' and data['type'] == 'update':
                                for item in data['data']:
                                    candle = item
                                    interval_begin = datetime.strptime(candle['interval_begin'].split('.')[0], '%Y-%m-%dT%H:%M:%S').replace(tzinfo=timezone.utc).timestamp()
                                    close_time = datetime.strptime(candle['timestamp'].split('.')[0], '%Y-%m-%dT%H:%M:%S').replace(tzinfo=timezone.utc).timestamp()
                                    ohlc_list = [
                                        interval_begin,
                                        close_time,
                                        candle['open'],
                                        candle['high'],
                                        candle['low'],
                                        candle['close'],
                                        candle['vwap'],
                                        candle['volume'],
                                        candle['trades'],
                                        candle['interval']
                                    ]
                                    full_data = [None, ohlc_list, f"ohlc-{candle['interval']}", candle['symbol']]
                                    channel_name = f"ohlc-{candle['interval']}-{candle['symbol']}"
                                    if channel_name in self.subscriptions:
                                        callback = self.subscriptions[channel_name]
                                        if asyncio.iscoroutinefunction(callback):
                                            await callback(full_data)
                                        else:
                                            callback(full_data)
                                        #await self.force_disconnect_and_reconnect()
                            elif channel == 'book':
                                book_type = data.get('type')
                                book_symbol = data['data'][0].get('symbol')
                                book_data = data['data'][0]
                                if book_symbol:
                                    for depth_key in self.subscriptions:
                                        if depth_key==('book-10-' + book_symbol):
                                            callback = self.subscriptions[depth_key]

                                            structured_book = {
                                                'symbol': book_symbol,
                                                'type': book_type,
                                                'timestamp': datetime.now(timezone.utc).isoformat(),
                                                'bids': book_data.get('bids', []),
                                                'asks': book_data.get('asks', []),
                                                'checksum': book_data.get('checksum', None)
                                            }
                                            if asyncio.iscoroutinefunction(callback):
                                                await callback(structured_book)
                                            else:
                                                callback(structured_book)
                            else:
                                # Handle other channels as needed
                                pass
                        else:
                            logger.warning(f"Received unknown message format: {data}")
                    except websockets.exceptions.ConnectionClosed:
                        logger.warning("WebSocket connection closed during message handling")
                        await self._notify_connection_listeners(False)
                        # Exit the loop and let the external reconnect logic handle it
                        return
                    except Exception as e:
                        logger.error(f"Error handling message: {e}")
                        await asyncio.sleep(1)
            except asyncio.CancelledError:
                logger.debug("Message handling task cancelled")
                self.running = False
                raise
            except Exception as e:
                logger.error(f"Message handling error: {e}")
                self.running = False
            finally:
                # If we've exited the message loop for any reason, try to reconnect
                if self.running and not self.is_connected():
                    await self.reconnect()

    async def subscribe_book(self, symbols: List[str], depth: int, callback: Callable[[Dict[str, Any]], None]):
        symbols = self.transform_kraken_pairs(symbols)
        subscription_message = {
            "method": "subscribe",
            "params": {
                "channel": "book",
                "symbol": symbols,
                "depth": depth
            }
        }
        if not self.ws or not self.is_connected():
            await self.reconnect()
        await self.ws.send(json.dumps(subscription_message))
        for symbol in symbols:
            channel_name = f"book-{depth}-{symbol}"
            self.subscriptions[channel_name] = callback
            logger.info(f"Registered callback for {channel_name}")

    async def subscribe_ohlc(self, symbols: List[str], interval: Union[int, str], callback: Callable[[Dict[str, Any]], None]):
        if isinstance(interval, int):
            interval_minutes = interval
        elif isinstance(interval, str):
            unit = ''.join(filter(str.isalpha, interval))  # Extract unit (e.g., 'min', 'h')
            value_str = ''.join(filter(str.isdigit, interval))  # Extract numeric part
            if not value_str or not unit:
                raise ValueError(f"Invalid interval format: {interval}")
            value = int(value_str)
            if unit not in INTERVAL_MAPPING:
                raise ValueError(f"Invalid unit in interval: {unit}")
            interval_minutes = value * INTERVAL_MAPPING[unit]  # Convert to minutes
        else:
            raise TypeError("Interval must be an integer or string")

        symbols = self.transform_kraken_pairs(symbols)
        subscription_message = {
            "method": "subscribe",
            "params": {
                "channel": "ohlc",
                "symbol": symbols,
                "interval": interval_minutes
            }
        }
        if not self.ws or not self.is_connected():
            await self.reconnect()
        await self.ws.send(json.dumps(subscription_message))
        for symbol in symbols:
            channel_name = f"ohlc-{interval_minutes}-{symbol}"
            self.subscriptions[channel_name] = callback
            logger.info(f"Registered callback for {channel_name}")

    async def subscribe_trade(self, symbols: List[str], callback: Callable[[Dict[str, Any]], None]):
        symbols = self.transform_kraken_pairs(symbols)
        subscription_message = {
            "method": "subscribe",
            "params": {
                "channel": "trade",
                "symbol": symbols
            }
        }
        if not self.ws or not self.is_connected():
            await self.reconnect()
        await self.ws.send(json.dumps(subscription_message))
        for symbol in symbols:
            channel_name = f"trade-{symbol}"
            self.subscriptions[channel_name] = callback
            logger.info(f"Registered callback for {channel_name}")

    async def subscribe_ticker(self, symbols: List[str], callback: Callable[[Dict[str, Any]], None]):
        symbols = self.transform_kraken_pairs(symbols)
        subscription_message = {
            "method": "subscribe",
            "params": {
                "channel": "ticker",
                "symbol": symbols
            }
        }
        if not self.ws or not self.is_connected():
            await self.reconnect()
        await self.ws.send(json.dumps(subscription_message))
        for symbol in symbols:
            channel_name = f"ticker-{symbol}"
            self.subscriptions[channel_name] = callback
            logger.info(f"Registered callback for {channel_name}")

    async def subscribe_spread(self, symbols: List[str], callback: Callable[[Dict[str, Any]], None]):
        symbols = self.transform_kraken_pairs(symbols)
        subscription_message = {
            "method": "subscribe",
            "params": {
                "channel": "spread",
                "symbol": symbols
            }
        }
        if not self.ws or not self.is_connected():
            await self.reconnect()
        await self.ws.send(json.dumps(subscription_message))
        for symbol in symbols:
            channel_name = f"spread-{symbol}"
            self.subscriptions[channel_name] = callback
            logger.info(f"Registered callback for {channel_name}")

    def transform_kraken_pairs(self, symbols: List[str]) -> List[str]:
        quote_currencies = {'USD', 'EUR', 'GBP', 'CAD', 'JPY', 'CHF', 'AUD'}
        transformed = []
        for pair in symbols:
            if '/' in pair:
                transformed.append(pair)
            else:
                if pair.startswith('X') or pair.startswith('Z'):
                    normalized_pair = pair[1:] if pair[0] in {'X', 'Z'} else pair
                    if normalized_pair[-3:] in quote_currencies:
                        base = normalized_pair[:-3]
                        quote = normalized_pair[-3:]
                        transformed_pair = f"{pair[0]}{base}/{quote}" if pair[0] in {'X', 'Z'} else f"{base}/{quote}"
                    else:
                        raise ValueError(f"Unable to parse pair: {pair}")
                else:
                    if len(pair) == 6 and pair[-3:] in quote_currencies:
                        base = pair[:3]
                        quote = pair[3:]
                        transformed_pair = f"{base}/{quote}"
                    else:
                        for i in range(len(pair) - 3, 0, -1):
                            if pair[i:] in quote_currencies:
                                base = pair[:i]
                                quote = pair[i:]
                                transformed_pair = f"{base}/{quote}"
                                break
                        else:
                            raise ValueError(f"Unable to parse pair: {pair}")
                transformed.append(transformed_pair)
        return transformed

    async def start(self):
        """Start the websocket client"""
        await self.connect()

    def start_sync(self):
        """Start the client in the current event loop"""
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self.connect())

    async def stop(self):
        """Stop the websocket client gracefully"""
        self.running = False
        await self.disconnect()

    async def disconnect(self):
        """Properly clean up resources"""
        if self.ws:
            try:
                await self.ws.close()
            except Exception as e:
                logger.error(f"Error closing websocket: {e}")
            self.ws = None

        if self.message_task and not self.message_task.done():
            self.message_task.cancel()
            try:
                await self.message_task
            except (asyncio.CancelledError, websockets.exceptions.ConnectionClosed):
                pass
        self.subscriptions = {}
        logger.info("WebSocket fully disconnected")

    async def force_disconnect_and_reconnect(self):
        """Force a disconnect and reconnect for testing"""
        logger.info("Forcing disconnect for testing reconnection")
        if self.ws:
            await self.ws.close()
        await asyncio.sleep(1)  # Give it a moment
        logger.info("Testing reconnection flow")

# Connection listener interface
class WebSocketConnectionListener:
    async def on_websocket_connected(self):
        """Called when the WebSocket connects or reconnects"""
        pass

    async def on_websocket_disconnected(self):
        """Called when the WebSocket disconnects"""
        pass