import psycopg2

class DatabaseSetup:
    def __init__(self, db_name, db_user, db_password, db_host, db_port):
        self.connection = psycopg2.connect(
            dbname=db_name,
            user=db_user,
            password=db_password,
            host=db_host,
            port=db_port
        )
        self.cursor = self.connection.cursor()

    def create_tables(self):
        # Create OHLC table for Kraken
        create_ohlc_table = """
        CREATE TABLE IF NOT EXISTS kraken_ohlc (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            open_price NUMERIC(20, 10) NOT NULL,
            high_price NUMERIC(20, 10) NOT NULL,
            low_price NUMERIC(20, 10) NOT NULL,
            close_price NUMERIC(20, 10) NOT NULL,
            volume NUMERIC(20, 10) NOT NULL,
            pair VARCHAR(16) NOT NULL,
            UNIQUE (timestamp, pair)
        );
        """

        # Create OHLC table for Binance
        create_binance_ohlc_table = """
        CREATE TABLE IF NOT EXISTS binance_ohlc (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            open_price NUMERIC(20, 10) NOT NULL,
            high_price NUMERIC(20, 10) NOT NULL,
            low_price NUMERIC(20, 10) NOT NULL,
            close_price NUMERIC(20, 10) NOT NULL,
            volume NUMERIC(20, 10) NOT NULL,
            pair VARCHAR(16) NOT NULL,
            UNIQUE (timestamp, pair)
        );
        """

        # Create live trades table
        create_live_trades_table = """
        CREATE TABLE IF NOT EXISTS live_trades (
            id SERIAL PRIMARY KEY,
            trade_id VARCHAR(64) NOT NULL,
            trade_id_match VARCHAR(64) UNIQUE NOT NULL,
            price NUMERIC(20, 10) NOT NULL,
            volume NUMERIC(20, 10) NOT NULL,
            value NUMERIC(20, 2) NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            pair VARCHAR(16) NOT NULL,
            buy_sell VARCHAR(4) NOT NULL,  -- 'buy' or 'sell'
            fee NUMERIC(20, 2) NOT NULL,
            reason VARCHAR(32),  -- 'take_profit', 'ema_crossover', or NULL for buy trades
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """

        # Create paper trades table
        create_paper_trades_table = """
        CREATE TABLE IF NOT EXISTS paper_trades (
            id SERIAL PRIMARY KEY,
            trade_id VARCHAR(64) NOT NULL,
            trade_id_match VARCHAR(64) UNIQUE NOT NULL,
            price NUMERIC(20, 10) NOT NULL,
            volume NUMERIC(20, 10) NOT NULL,
            value NUMERIC(20, 2) NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            pair VARCHAR(16) NOT NULL,
            buy_sell VARCHAR(4) NOT NULL,  -- 'buy' or 'sell'
            fee NUMERIC(20, 2) NOT NULL,
            reason VARCHAR(32),  -- 'take_profit', 'ema_crossover', or NULL for buy trades
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """

        # Create backtest trades table
        create_backtest_trades_table = """
        CREATE TABLE IF NOT EXISTS backtest_trades (
            id SERIAL PRIMARY KEY,
            trade_id VARCHAR(64) NOT NULL,
            trade_id_match VARCHAR(64) UNIQUE NOT NULL,
            price NUMERIC(20, 10) NOT NULL,
            volume NUMERIC(20, 10) NOT NULL,
            value NUMERIC(20, 2) NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            pair VARCHAR(16) NOT NULL,
            buy_sell VARCHAR(4) NOT NULL,  -- 'buy' or 'sell'
            fee NUMERIC(20, 2) NOT NULL,
            reason VARCHAR(32),  -- 'take_profit', 'ema_crossover', or NULL for buy trades
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """

        # Create backtest results table
        create_backtest_results_table = """
        CREATE TABLE IF NOT EXISTS backtest_results (
            id SERIAL PRIMARY KEY,
            strategy_name VARCHAR(64) NOT NULL,
            entry_trade_id VARCHAR(64) NOT NULL,
            exit_trade_id VARCHAR(64) NOT NULL,
            entry_price NUMERIC(20, 10) NOT NULL,
            exit_price NUMERIC(20, 10) NOT NULL,
            entry_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            exit_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            volume NUMERIC(20, 10) NOT NULL,
            profit_loss NUMERIC(20, 2) NOT NULL,
            duration INTERVAL NOT NULL,  -- Duration of the trade
            success BOOLEAN NOT NULL,     -- True if profitable, false otherwise
            exit_reason VARCHAR(32) NOT NULL,  -- 'take_profit' or 'ema_crossover'
            FOREIGN KEY (entry_trade_id) REFERENCES backtest_trades(trade_id_match) ON DELETE CASCADE,
            FOREIGN KEY (exit_trade_id) REFERENCES backtest_trades(trade_id_match) ON DELETE CASCADE,
            UNIQUE (entry_trade_id, exit_trade_id)
        );
        """

        # Execute table creation scripts
        self.cursor.execute(create_binance_ohlc_table)
        self.cursor.execute(create_ohlc_table)
        self.cursor.execute(create_live_trades_table)
        self.cursor.execute(create_paper_trades_table)
        self.cursor.execute(create_backtest_trades_table)
        self.cursor.execute(create_backtest_results_table)
        self.connection.commit()

    def update_existing_tables(self):
        """Add new columns to existing tables if they don't exist"""
        alter_statements = [
            """
            DO $$ 
            BEGIN 
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                             WHERE table_name = 'live_trades' AND column_name = 'reason') THEN
                    ALTER TABLE live_trades ADD COLUMN reason VARCHAR(32);
                END IF;
            END $$;
            """,
            """
            DO $$ 
            BEGIN 
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                             WHERE table_name = 'paper_trades' AND column_name = 'reason') THEN
                    ALTER TABLE paper_trades ADD COLUMN reason VARCHAR(32);
                END IF;
            END $$;
            """,
            """
            DO $$ 
            BEGIN 
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                             WHERE table_name = 'backtest_trades' AND column_name = 'reason') THEN
                    ALTER TABLE backtest_trades ADD COLUMN reason VARCHAR(32);
                END IF;
            END $$;
            """,
            """
            DO $$ 
            BEGIN 
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                             WHERE table_name = 'backtest_results' AND column_name = 'exit_reason') THEN
                    ALTER TABLE backtest_results ADD COLUMN exit_reason VARCHAR(32) NOT NULL DEFAULT 'ema_crossover';
                END IF;
            END $$;
            """
        ]
        
        for statement in alter_statements:
            self.cursor.execute(statement)
        self.connection.commit()

    def close(self):
        self.cursor.close()
        self.connection.close()