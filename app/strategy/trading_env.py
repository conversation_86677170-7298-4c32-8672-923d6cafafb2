from typing import Optional, Any
import numpy as np
import pandas as pd
from app.trade_manager.portfolio_manager import PortfolioManager
from app.trade_manager.trade_manager import TradeManager
from app.strategy.base_strategy import BaseStrategy

# Import RL libraries
import gymnasium as gym
from gymnasium import spaces


class TradingEnv(gym.Env):
    """Custom Trading Environment for PPO agent training with comprehensive reward mechanism"""

    def __init__(self, df: pd.DataFrame, strategy: Optional[BaseStrategy] = None, initial_balance: float = 5000,
                 portfolio_manager: Optional[PortfolioManager] = None, trade_manager: Optional[TradeManager] = None,
                 reward_weights: Optional[dict] = None, position_size_multiplier: float = 20.0, volatility_multiplier: float = 3.0):
        super().__init__()
        self.reward_weights = reward_weights or {
            'return_component': 2.0,
            'sharpe_component': 0.8,
            'sortino_component': 0.7,
            'drawdown_penalty': 0.38,
            'trading_penalty': 0.2,
            'market_alignment': 0.5,
            'profit_taking': 1.75,
            'cash_management': 0.37
        }

        # Data and balance tracking
        self.strategy = strategy
        if strategy is None:
            self.df = df.copy()
        else:
            self.df = strategy.preprocess_data(df.copy())
        self.initial_balance = initial_balance
        self.max_steps = len(df) - 1
        self.current_step = 0
        self.shares_held = 0
        self.current_price = None
        self.net_worth_history = [initial_balance]
        self.market_regime = 0
        self.last_buy_price = None
        self.volatility = 0.02  # Default volatility

        # For advanced reward calculation
        self.transaction_fee = 0.0026  # 0.26% per trade
        self.window_size = 20
        self.last_action = 0

        # Spaces definition
        self.action_space = spaces.Discrete(3)  # Buy, hold, sell
        num_features = self._calculate_num_features()
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, shape=(num_features,), dtype=np.float32
        )
        self.strategy = strategy
        if portfolio_manager is not None:
            self.portfolio_manager = portfolio_manager
        else:
            # Default values if strategy is None
            asset = "SOL"
            base_currency = "USD"
            if self.strategy is not None and hasattr(self.strategy, 'pair'):
                asset = self.strategy.pair[0:3]
                base_currency = self.strategy.pair[3:6]

            self.portfolio_manager = PortfolioManager(
                initial_balance=initial_balance,
                api=None,
                asset=asset,
                base_currency=base_currency
            )
        if trade_manager is not None:
            self.trade_manager = trade_manager
        else:
            self.trade_manager = TradeManager(
                portfolio_manager=self.portfolio_manager,
                order_executor=None,
                trade_history=None,
                api=None,
                mode='backtest'
            )
        self.info_list = []
        self.returns_window = []
        self.action_history = []
        self.position_size_multiplier = position_size_multiplier
        self.volatility_multiplier = volatility_multiplier

    def _calculate_num_features(self):
        return 28  # Adjust this based on the number of features in your observation space

    def reset(self, *, seed: int | None = None, options: dict[str, Any] | None = None) -> tuple[np.ndarray, dict]:
        """Reset the environment to the initial state. Compatible with Gymnasium API.

        Args:
            seed: Random seed for reproducibility
            options: Additional options for environment reset

        Returns:
            Tuple of (observation, info)
        """
        self.shares_held = 0
        self.current_step = 0
        self.last_action = 0
        # Fixed: use self.net_worth_history instead of self.strategy.net_worth_history
        self.net_worth_history = [self.portfolio_manager.initial_balance]
        # Fixed: use self.returns_window instead of self.strategy.returns_window
        self.returns_window = []
        if seed is not None:
            np.random.seed(seed)
            super().reset(seed=seed, options=options)

        # Initialize additional properties
        self.last_buy_price = None
        self.volatility = 0.02
        self.market_regime = 0

        # Default values if strategy is None
        asset = "SOL"
        base_currency = "USD"
        if self.strategy is not None and hasattr(self.strategy, 'pair'):
            asset = self.strategy.pair[0:3]
            base_currency = self.strategy.pair[3:6]

        # Initialize portfolio and trade managers if not already set
        if not hasattr(self, 'portfolio_manager') or self.portfolio_manager is None:
            self.portfolio_manager = PortfolioManager(
                initial_balance=self.initial_balance,
                api=None,
                asset=asset,
                base_currency=base_currency
            )

        if not hasattr(self, 'trade_manager') or self.trade_manager is None:
            self.trade_manager = TradeManager(
                portfolio_manager=self.portfolio_manager,
                order_executor=None,
                trade_history=None,
                api=None,
                mode='backtest'
            )

        self.episode_info = []

        # Return observation and info as required by Gymnasium API
        return self._next_observation(), {}

    def _next_observation(self):
        """Return the current observation (state)"""
        if self.current_step >= len(self.df):
            # Handle the edge case where we're beyond the dataframe bounds
            return np.zeros(self._calculate_num_features(), dtype=np.float32)

        frame = self.df.iloc[self.current_step]

        # Ensure order book features are present in every row, fill missing with 0.0
        ob_features = [
            'ob_spread', 'ob_spread_pct', 'ob_mid_price', 'ob_volume_imbalance',
            'ob_bid_depth_5', 'ob_ask_depth_5', 'ob_total_bid_vol', 'ob_total_ask_vol',
            'ob_bid_impact', 'ob_ask_impact', 'ob_weighted_bid', 'ob_weighted_ask'
        ]
        for feature in ob_features:
            if feature not in self.df.columns:
                self.df[feature] = 0.0
            if f"{feature}_norm" not in self.df.columns:
                self.df[f"{feature}_norm"] = 0.0

        # Update volatility calculation
        self.volatility = self.calculate_volatility()

        # Extract market regime
        market_regime = float(frame.get('market_regime', 0))
        self.market_regime = market_regime

        # Calculate potential max shares based on current price
        max_shares = self.portfolio_manager.get_current_total_value(frame.get(
            "close_price", 0.0)) / frame['close_price'] if frame['close_price'] > 0 else 1.0

        # Create a normalized and complete observation vector
        obs = np.array([
            # Price and volume basics
            # frame.get('open_price_norm', 0.0),
            # frame.get('high_price_norm', 0.0),
            # frame.get('low_price_norm', 0.0),
            # frame.get('close_price_norm', 0.0),
            # frame.get('volume_norm', 0.0),

            # Trend indicators
            # frame.get('EMA_short_norm', 0.0),
            # frame.get('EMA_long_norm', 0.0),
            # frame.get('macd_norm', 0.0),
            frame.get('macd_signal_norm', 0.0),
            # frame.get('macd_hist_norm', 0.0),
            frame.get('sar_norm', 0.0),

            # Momentum indicators
            # frame.get('rsi_norm', 0.0),
            # frame.get('momentum_norm', 0.0),
            # frame.get('stoch_k_norm', 0.0),
            # frame.get('stoch_d_norm', 0.0),
            # frame.get('cci_norm', 0.0),
            # frame.get('roc_norm', 0.0),
            frame.get('mfi_norm', 0.0),

            # Volatility indicators
            frame.get('adx_norm', 0.0),
            frame.get('atr_norm', 0.0),
            frame.get('bb_upper_norm', 0.0),
            frame.get('bb_middle_norm', 0.0),
            frame.get('bb_lower_norm', 0.0),
            # frame.get('bb_percent_b_norm', 0.0),
            frame.get('volatility_ratio_norm', 0.0),

            # Volume indicators
            frame.get('vwap_norm', 0.0),
            frame.get('obv_norm', 0.0),
            # frame.get('relative_volume_norm', 0.0),
            frame.get('volume_roc_norm', 0.0),
            frame.get('stddev_returns_norm', 0.0),
            frame.get('ad_line_norm', 0.0),
            frame.get('adosc_norm', 0.0),

            # Volatility bands
            frame.get('kc_upper_norm', 0.0),
            # frame.get('kc_middle_norm', 0.0),
            frame.get('kc_lower_norm', 0.0),
            frame.get('vwap_slope_norm', 0.0),
            frame.get('obv_slope_norm', 0.0),

            # Order book features (normalized)
            frame.get('ob_spread_pct_norm', 0.0),
            frame.get('ob_bid_depth_5_norm', 0.0),
            frame.get('ob_volume_imbalance_norm', 0.0),
            frame.get('ob_total_bid_vol_norm', 0.0),
            frame.get('ob_total_ask_vol_norm', 0.0),

            # Position and portfolio information
            sum(trade.remaining_volume for trade in self.trade_manager.active_trades.values(
            )) / max_shares if max_shares > 0 else 0.0,
            self.portfolio_manager.current_balance /
            self.portfolio_manager.initial_balance if self.portfolio_manager.initial_balance > 0 else 0.0,
            (self.portfolio_manager.get_current_total_value(frame.get("close_price", 0.0))) /
            self.portfolio_manager.initial_balance if self.portfolio_manager.initial_balance > 0 else 0.0,
            market_regime

            # Candlestick patterns
            # float(frame.get('bullish_engulfing', 0)),
            # float(frame.get('bearish_engulfing', 0)),
            # float(frame.get('hammer', 0)),
            # float(frame.get('hanging_man', 0)),
            # float(frame.get('doji', 0)),
            # float(frame.get('morning_star', 0)),
            # float(frame.get('evening_star', 0)),
            # float(frame.get('marubozu', 0)),
            # float(frame.get('three_white_soldiers', 0)),
            # float(frame.get('three_black_crows', 0))
        ], dtype=np.float32)

        # Ensure the observation matches the expected size
        assert len(obs) == self._calculate_num_features(
        ), f"Observation size {len(obs)} doesn't match expected size {self._calculate_num_features()}"

        return obs

    def step(self, action: int) -> tuple[np.ndarray, float, bool, bool, dict[str, Any]]:
        """
        Enhanced step function that mimics the process_candle method from BaseStrategy
        and calculates the reward based on the outcome.

        Args:
            action: Action to take (0=hold, 1=buy, 2=sell)

        Returns:
            Tuple of (next_observation, reward, terminated, truncated, info_dict)
        """
        if self.current_step >= len(self.df) - 1:
            return self._next_observation(), 0, True, True, {}

        # Store state before action
        # previous_action is not used but kept for clarity
        _ = self.last_action
        previous_net_worth = self.portfolio_manager.get_current_total_value(
            self.current_price) if self.current_price else 0

        # Get current and previous candles for strategy processing
        candle = self.df.iloc[self.current_step]
        previous_candle = self.df.iloc[max(
            0, self.current_step - 1)] if self.current_step > 0 else candle
        # minusthree_candle is not used in this method but might be needed in future extensions
        _ = self.df.iloc[max(0, self.current_step - 3)
                         ] if self.current_step > 2 else previous_candle

        # Store current price
        self.current_price = candle['close_price']

        # Update volatility
        self.volatility = self.calculate_volatility()

        # Store the action for reward calculation
        self.last_action = action

        # ==== MANAGE ACTIVE TRADES (similar to BaseStrategy.manage_active_trades) ====
        # Keep track of trades that were completed
        trades_completed = 0

        # Get all active trades
        active_trade_ids = list(self.trade_manager.active_trades.keys())
        if not active_trade_ids:
            self.last_buy_price = None

        for trade_id in active_trade_ids:
            if trade_id not in self.trade_manager.active_trades:
                continue

            trade = self.trade_manager.active_trades[trade_id]

            # --------------------------------------------
            # 1. Handle Take Profits FIRST
            # --------------------------------------------
            # Check if strategy is available and has the required methods
            if self.strategy is not None and hasattr(self.strategy, 'get_take_profit_levels'):
                for i, target in enumerate(self.strategy.get_take_profit_levels()):
                    if (
                        not trade.reached_profit_levels[i] and
                        hasattr(self.strategy, 'check_take_profit') and
                        self.strategy.check_take_profit(trade.entry_price, self.current_price, target) and
                        not action == 2  # Avoid selling if action is to sell
                    ):
                        exit_price = self.current_price
                        # 25% per TP level
                        exit_volume = round(trade.initial_volume * 0.25, 5)
                        if exit_volume > trade.remaining_volume:
                            exit_volume = trade.remaining_volume

                        # Get fee rate safely
                        fee_rate = 0.0026  # Default fee rate
                        if hasattr(self.strategy, 'taker_maker_fee'):
                            fee_rate = self.strategy.taker_maker_fee

                        fee = round(exit_price * exit_volume * fee_rate, 5)
                        self.trade_manager.partial_exit(
                            trade_id=trade_id,
                            exit_price=exit_price,
                            exit_volume=exit_volume,
                            timestamp=candle["timestamp"],
                            fee=fee,
                            exit_reason=f'take_profit_{target*100:.1f}%'
                        )
                        trades_completed += 1
                        trade.reached_profit_levels[i] = True

                    # Enable trailing stop after last TP
                    # if i == len(self.strategy.get_take_profit_levels()) - 1:
                    trade.trailing_stop_on = True

            # --------------------------------------------
            # 2. Handle Trailing Stop
            # --------------------------------------------
            if trade.trailing_stop_on:
                if self.current_price > trade.max_price:
                    trade.max_price = self.current_price
                    # Get stop loss value safely
                    stop_loss = -0.05  # Default stop loss of 5%
                    if self.strategy is not None and hasattr(self.strategy, 'get_stop_loss') and callable(getattr(self.strategy, 'get_stop_loss')):
                        stop_loss = self.strategy.get_stop_loss()

                    trade.trailing_price = trade.max_price * (
                        1 + (stop_loss * 0.25 * len(trade.reached_profit_levels)))

            # --------------------------------------------
            # 3. Handle Stop Loss/Trailing Stop
            # --------------------------------------------
            stop_loss_triggered = False
            if self.strategy is not None and hasattr(self.strategy, 'check_stop_loss') and callable(getattr(self.strategy, 'check_stop_loss')):
                stop_loss_triggered = self.strategy.check_stop_loss(
                    trade.entry_price, self.current_price)

            if (
                (stop_loss_triggered or
                 (trade.trailing_stop_on and self.current_price <= trade.trailing_price)) and
                not action == 2
            ):
                exit_volume = trade.remaining_volume

                # Get fee rate safely
                fee_rate = 0.0026  # Default fee rate
                if self.strategy is not None and hasattr(self.strategy, 'taker_maker_fee'):
                    fee_rate = self.strategy.taker_maker_fee

                fee = round(self.current_price * exit_volume * fee_rate, 5)
                self.trade_manager.complete_exit(
                    trade_id=trade_id,
                    exit_price=self.current_price,
                    timestamp=candle["timestamp"],
                    fee=fee,
                    exit_reason="stop_loss" if not trade.trailing_stop_on else "trailing_stop_loss"
                )
                trades_completed += 1

            # --------------------------------------------
            # 4. Handle Technical Exit LAST (only if trade still exists)
            # --------------------------------------------
            if (
                trade_id in self.trade_manager.active_trades and
                action == 2  # Sell signal from the model
            ):
                # Get fee rate safely
                fee_rate = 0.0026  # Default fee rate
                if self.strategy is not None and hasattr(self.strategy, 'taker_maker_fee'):
                    fee_rate = self.strategy.taker_maker_fee

                fee = round(self.current_price *
                            trade.remaining_volume * fee_rate, 5)
                self.trade_manager.complete_exit(
                    trade_id=trade_id,
                    exit_price=self.current_price,
                    timestamp=candle["timestamp"],
                    fee=fee,
                    exit_reason="exit_signal"
                )
                trades_completed += 1

        # ==== EXECUTE NEW TRADE IF BUY SIGNAL ====
        if action == 1 and self.strategy is not None:
            # Default values if strategy doesn't have these attributes
            min_investment = 200
            taker_maker_fee = 0.0025
            pair = "SOLUSD"
            take_profit_levels = [0.03, 0.05, 0.08]

            # Get values from strategy if available
            if hasattr(self.strategy, 'min_investment'):
                min_investment = self.strategy.min_investment
            if hasattr(self.strategy, 'taker_maker_fee'):
                taker_maker_fee = self.strategy.taker_maker_fee
            if hasattr(self.strategy, 'pair'):
                pair = self.strategy.pair
            if hasattr(self.strategy, 'get_take_profit_levels') and callable(getattr(self.strategy, 'get_take_profit_levels')):
                take_profit_levels = self.strategy.get_take_profit_levels()

            if self.portfolio_manager.current_balance >= min_investment:
                # Calculate optimal position size based on Kelly Criterion
                # This dynamically adjusts trade size based on account balance and market conditions
                risk_percentage = 0.02
                if hasattr(self.strategy, 'calculate_kelly_fraction') and callable(getattr(self.strategy, 'calculate_kelly_fraction')):
                    risk_percentage = self.strategy.calculate_kelly_fraction()

                # Volatility adjustment - reduce position size in high volatility
                if self.volatility > 0:
                    risk_percentage = max(0.02, min(
                        0.1, risk_percentage / (self.volatility * self.volatility_multiplier)))

                # Maximum position size
                investment_amount = self.portfolio_manager.current_balance * risk_percentage * \
                    self.position_size_multiplier  # Allow up to 70% of balance per position

                # Check for drawdown conditions
                is_in_drawdown = False
                if hasattr(self, 'net_worth_history') and len(self.net_worth_history) > 0:
                    portfolio_peak = max(self.net_worth_history)
                    current_net_worth = self.portfolio_manager.get_current_total_value(
                        self.current_price)
                    is_in_drawdown = current_net_worth < 0.9 * portfolio_peak

                if is_in_drawdown:
                    investment_amount *= 0.8

                # Calculate fees and actual shares to buy
                fee = round(investment_amount * taker_maker_fee, 5)
                total_cost = investment_amount + fee

                # Ensure sufficient funds
                if self.portfolio_manager.current_balance >= total_cost and total_cost >= min_investment:
                    volume = round(investment_amount / self.current_price, 5)

                    # Get strategy name
                    strategy_name = "UnknownStrategy"
                    if hasattr(self.strategy, '__class__') and hasattr(self.strategy.__class__, '__name__'):
                        strategy_name = self.strategy.__class__.__name__

                    # Open the trade
                    self.trade_manager.open_trade(
                        price=self.current_price,
                        volume=volume,
                        timestamp=candle["timestamp"],
                        pair=pair,
                        fee=fee,
                        strategy_name=strategy_name,
                        entry_reason="strategy_entry",
                        take_profit_levels=take_profit_levels
                    )

                    self.last_buy_price = self.current_price

        # Move to next step AFTER trade execution
        self.current_step += 1
        done = self.current_step >= len(self.df) - 1

        # Calculate current net worth
        current_net_worth = self.portfolio_manager.get_current_total_value(
            self.current_price)
        self.net_worth_history.append(current_net_worth)

        # Store the step return for reward calculation
        step_return = (current_net_worth - previous_net_worth) / \
            previous_net_worth if previous_net_worth > 0 else 0

        # Add to returns window for metrics
        if not hasattr(self, 'returns_window'):
            self.returns_window = []
        self.returns_window.append(step_return)
        while len(self.returns_window) > self.window_size:
            self.returns_window.pop(0)

        # Update action history
        if not hasattr(self, 'action_history'):
            self.action_history = []
        self.action_history.append(action)
        while len(self.action_history) > 30:
            self.action_history.pop(0)

        # Calculate reward using the strategy's reward calculation if available
        if hasattr(self, '_calculate_reward'):
            reward = self._calculate_reward(
                step_return,
                current_net_worth,
                self.df.iloc[self.current_step -
                             1] if self.current_step > 0 else self.df.iloc[0],
                self.df.iloc[max(0, self.current_step-2)]
            )
        else:
            # Default reward is just the step return
            reward = step_return * 100  # Scale up for better learning

        # Build detailed info dictionary
        info_dict = {
            'net_worth': current_net_worth,
            'balance': self.portfolio_manager.current_balance,
            'shares_held': self.shares_held,
            'current_price': self.current_price,
            'step_return': step_return,
            'volatility': self.volatility,
            'market_regime': self.market_regime if hasattr(self, 'market_regime') else 0
        }

        self.shares_held = sum(
            trade.remaining_volume for trade in self.trade_manager.active_trades.values())

        # Get next observation after executing actions
        next_obs = self._next_observation()

        return next_obs, reward, done, False, info_dict

    def render(self, mode: str = 'human'):
        """
        Render the environment (optional)

        Args:
            mode: Rendering mode, only 'human' is supported (parameter required by Gym API)

        Returns:
            str: String representation of the current state
        """
        # Suppress unused parameter warning
        _ = mode
        profit = self.net_worth_history[-1] - self.initial_balance
        return f"Step: {self.current_step}, Net Worth: {self.net_worth_history[-1]:.2f}, Profit: {profit:.2f}"

    def calculate_volatility(self, window_size=20):
        if len(self.df) <= self.current_step:
            return 0.02  # Default value if we don't have enough data

        # Get the window of data to calculate volatility
        end_idx = min(self.current_step, len(self.df) - 1)
        start_idx = max(0, end_idx - window_size)

        if end_idx <= start_idx:
            return 0.02  # Not enough data

        # Extract price data for the window
        price_window = self.df.iloc[start_idx:end_idx+1]['close_price'].values

        if len(price_window) < 2:
            return 0.02

        # Calculate returns
        # Convert to numpy array first to avoid type issues
        price_array = np.array(price_window, dtype=float)
        returns = np.diff(price_array) / price_array[:-1]

        # Calculate volatility (standard deviation of returns)
        volatility = np.std(returns)

        # Annualize and normalize to a reasonable range
        # For hourly data, multiply by sqrt(24*365) for annualization
        interval = '1h'  # Default to hourly
        if self.strategy is not None and hasattr(self.strategy, 'interval'):
            interval = self.strategy.interval

        if interval == '1h':
            volatility = volatility * np.sqrt(24 * 365)
        elif interval == '1d':
            volatility = volatility * np.sqrt(365)

        # Ensure a minimum level of volatility and cap it
        # Convert to float to avoid type issues
        volatility_float = float(volatility)
        volatility = max(0.01, min(0.2, volatility_float))

        return volatility

    def _calculate_reward(self, step_return: float, current_net_worth: float, candle: pd.Series, previous_candle: pd.Series) -> float:
        """
        Calculate the reinforcement learning reward signal with comprehensive components.

        Args:
            step_return: Return for the current step
            current_net_worth: Current portfolio value
            candle: Current candle data
            previous_candle: Previous candle data

        Returns:
            float: Calculated reward value
        """
        # --- Enhanced Reward Components ---

        # 1. Risk-adjusted return component (base)
        # Amplify signal to balance with other components
        return_component = step_return * 50

        # 2. Enhanced Sharpe ratio with longer window
        sharpe_component = 0
        if hasattr(self, 'returns_window') and len(self.returns_window) >= 5:
            returns_mean = np.mean(self.returns_window)
            returns_std = np.std(self.returns_window) + \
                1e-9  # Avoid division by zero
            # Scale Sharpe to appropriate range relative to return_component
            sharpe_ratio = returns_mean / returns_std
            # Tanh to keep bounded between -1 and 1, then scale
            sharpe_component = np.tanh(sharpe_ratio) * 2.0

        # 3. Sortino ratio (penalize only downside deviation)
        sortino_component = 0
        if hasattr(self, 'returns_window') and len(self.returns_window) >= 5:
            negative_returns = [r for r in self.returns_window if r < 0]
            if negative_returns:
                downside_deviation = np.std(negative_returns) + 1e-9
                returns_mean = np.mean(self.returns_window)
                sortino_ratio = returns_mean / downside_deviation
                # Slightly less than Sharpe
                sortino_component = np.tanh(sortino_ratio) * 1.5

        # 4. Drawdown penalty (maximum underwater period)
        drawdown_penalty = 0
        if hasattr(self, 'net_worth_history') and len(self.net_worth_history) > 2:
            # Calculate drawdown from peak
            current_drawdown = 1 - \
                (current_net_worth / max(self.net_worth_history))
            drawdown_penalty = -current_drawdown * 3.0  # Higher penalty for deep drawdowns

        # 5. More sophisticated trading frequency penalty with adaptive threshold
        trading_frequency = 0
        trading_penalty = 0
        if hasattr(self, 'action_history') and len(self.action_history) > 0:
            trading_count = sum(1 for a in self.action_history[-15:] if a != 0)
            expected_trading_rate = 0.35  # Expect trading ~20% of the time

            # Volatility-adjusted trading rate
            # Trade more in volatile markets, less in calm markets
            if hasattr(self, 'volatility') and self.volatility > 0:
                expected_trading_rate = min(0.5, max(0.1, self.volatility * 2))

            trading_frequency = trading_count / \
                min(15, len(self.action_history))

            # Penalize deviation from expected trading rate (both too much and too little trading)
            trading_penalty = -abs(trading_frequency -
                                   expected_trading_rate) * 2.0

        # 6. Market regime alignment with increased weight
        market_alignment_bonus = 0
        market_regime = 0
        if hasattr(candle, 'get') and 'market_regime' in candle:
            market_regime = candle.get('market_regime', 0)
            if hasattr(previous_candle, 'get'):
                market_regime = previous_candle.get(
                    'market_regime', market_regime)

            # Better aligning actions with market conditions
            if market_regime > 0:  # Bullish
                if hasattr(self, 'last_action') and self.last_action == 1:  # Buying in bull market
                    market_alignment_bonus = 1.0
                elif hasattr(self, 'last_action') and self.last_action == 2:  # Selling in bull market
                    market_alignment_bonus = -0.5  # Penalty but smaller than buying in bear
            elif market_regime < 0:  # Bearish
                if hasattr(self, 'last_action') and self.last_action == 2:  # Selling in bear market
                    market_alignment_bonus = 1.0
                elif hasattr(self, 'last_action') and self.last_action == 1:  # Buying in bear market
                    market_alignment_bonus = -0.4  # Stronger penalty

        # 7. Profit-taking incentive
        profit_taking_bonus = 0
        if hasattr(self, 'last_action') and self.last_action == 2 and hasattr(self, 'shares_held') and self.shares_held > 0 and hasattr(self, 'last_buy_price') and self.last_buy_price is not None:
            if hasattr(self, 'current_price') and self.current_price is not None and self.current_price > 0:
                profit_percentage = (self.current_price -
                                     self.last_buy_price) / self.last_buy_price
                if profit_percentage > 0.03:  # 3% profit or more
                    profit_taking_bonus = min(
                        1.0, profit_percentage * 10)  # Cap at 1.0

        # 8. Cash management incentive
        # Reward maintaining some cash during high volatility or bearish regimes
        cash_management_bonus = 0
        if hasattr(self.portfolio_manager, 'current_balance') and current_net_worth > 0:
            cash_ratio = self.portfolio_manager.current_balance / current_net_worth

            if (hasattr(self, 'volatility') and self.volatility > 0.02) or market_regime < 0:
                # In high volatility or bearish regimes, reward keeping ~30-50% cash
                optimal_cash = 0.4  # Target 40% cash
                cash_management_bonus = 0.5 * \
                    (1 - abs(cash_ratio - optimal_cash) / optimal_cash)

        # Final reward calculation with balanced weights
        reward = (
            return_component * self.reward_weights['return_component'] +
            sharpe_component * self.reward_weights['sharpe_component'] +
            sortino_component * self.reward_weights['sortino_component'] +
            drawdown_penalty * self.reward_weights['drawdown_penalty'] +
            trading_penalty * self.reward_weights['trading_penalty'] +
            market_alignment_bonus * self.reward_weights['market_alignment'] +
            profit_taking_bonus * self.reward_weights['profit_taking'] +
            cash_management_bonus * self.reward_weights['cash_management']
        )

        # Prevent extreme reward values that could destabilize learning
        reward = np.clip(reward, -10.0, 10.0)

        return reward
