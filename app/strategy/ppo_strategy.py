import logging
import numpy as np
import pandas as pd
import os
import re
from datetime import datetime
from typing import Dict, Any, Optional
from stable_baselines3 import PPO
from .base_strategy import BaseStrategy
from app.utils.indicator_calculator import IndicatorCalculator
from app.trade_manager.portfolio_manager import PortfolioManager
from app.trade_manager.trade_manager import TradeManager
from .trading_env import TradingEnv

logger = logging.getLogger(__name__)

class PPOStrategy(BaseStrategy):
    """RL-based strategy using Proximal Policy Optimization (PPO)"""

    def __init__(self, pair: str,
                 train_on_startup: bool = False,  # Whether to train on initialization
                 short_window: int = 7,
                 long_window: int = 21,
                 take_profit_levels: list = [0.05, 0.1, 0.15],
                 stop_loss: float = -0.2,
                 interval: str = '1h',
                 max_investment: float = 7500,
                 taker_maker_fee: float = 0.0026,
                 min_investment: float = 250,
                 position_size_multiplier: float = 20.0,
                 volatility_multiplier: float = 3.0):

        super().__init__(
            pair=pair,
            take_profit_levels=take_profit_levels,
            stop_loss=stop_loss,
            interval=interval,
            max_investment=max_investment,
            taker_maker_fee=taker_maker_fee,
            min_investment=min_investment,
            position_size_multiplier=position_size_multiplier,
            volatility_multiplier=volatility_multiplier
        )

        self.short_window = short_window
        self.long_window = long_window
        self.model_path = self.find_newest_model()
        self.train_on_startup = train_on_startup
        self.model = None
        self.env = None
        self.portfolio_manager = None
        self.trade_manager = None
        self.historical_data = pd.DataFrame()
        self.last_action = 0  # 0 = hold, 1 = buy, 2 = sell
        self.retrain_interval_days = 7  # Days between retraining
        self.last_training_time: Optional[datetime] = None  # Last time the model was trained

        # Initialize model if path is provided
        if self.model_path:
            try:
                self.model = PPO.load(self.model_path)
                logger.info(f"Loaded PPO model from {self.model_path}")
            except Exception as e:
                logger.error(f"Failed to load model: {e}")

    def find_newest_model(self, models_folder="models"):
        pattern = re.compile(r"ppo_SOLUSD_(\d{8}_\d{6})\.zip")
        newest_model = None
        newest_timestamp = None

        for filename in os.listdir(models_folder):
            match = pattern.match(filename)
            if match:
                timestamp_str = match.group(1)
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                if newest_timestamp is None or timestamp > newest_timestamp:
                    newest_timestamp = timestamp
                    newest_model = filename

        if newest_model:
            return os.path.join(models_folder, newest_model)
        else:
            return None  # No matching models found

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepares data before running the strategy"""
        logger.info("Preprocessing data for PPO strategy")
        cols_to_convert = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
        df[cols_to_convert] = df[cols_to_convert].apply(pd.to_numeric, errors='coerce')
        #df = self.resample_data(df)

        # --- Calculate indicators ---
        # Trend
        df["EMA_short"] = IndicatorCalculator.calculate_ema(df, 7)
        df["EMA_long"] = IndicatorCalculator.calculate_ema(df, 21)

        # New Trend Indicators
        df["macd"], df["macd_signal"], df["macd_hist"] = IndicatorCalculator.calculate_macd(df)
        df["sar"] = IndicatorCalculator.calculate_parabolic_sar(df)

        # Momentum
        df["rsi"] = IndicatorCalculator.calculate_rsi(df)
        df["momentum"] = IndicatorCalculator.calculate_momentum(df, 10)
        df["stoch_k"], df["stoch_d"] = IndicatorCalculator.calculate_stochastic(df)
        df["cci"] = IndicatorCalculator.calculate_commodity_channel_index(df)
        df["roc"] = IndicatorCalculator.calculate_rate_of_change(df)

        # Volatility
        df["atr"] = IndicatorCalculator.calculate_atr(df)
        df["adx"] = IndicatorCalculator.calculate_average_directional_index(df)
        df["bb_upper"], df["bb_middle"], df["bb_lower"] = IndicatorCalculator.calculate_bollinger_bands(df)
        df["bb_percent_b"] = IndicatorCalculator.calculate_bollinger_percent_b(df)
        df["volatility_ratio"] = IndicatorCalculator.calculate_volatility_ratio(df)

        # Volume
        df["vwap"] = IndicatorCalculator.calculate_vwap(df)
        df["obv"] = IndicatorCalculator.calculate_on_balance_volume(df)
        df["relative_volume"] = IndicatorCalculator.calculate_relative_volume(df)
        df["volume_roc"] = IndicatorCalculator.calculate_volume_roc(df)
        df["stddev_returns"] = IndicatorCalculator.calculate_stddev_of_returns(df)
        df["mfi"] = IndicatorCalculator.calculate_money_flow_index(df)
        df["ad_line"] = IndicatorCalculator.calculate_chaikin_ad_line(df)
        df["adosc"] = IndicatorCalculator.calculate_chaikin_oscillator(df)
        df['vwap_slope']= IndicatorCalculator.calculate_vwap_slope(df)
        df['obv_slope'] = IndicatorCalculator.calculate_obv_slope(df)

        # Volatility Bands
        df["kc_upper"], df["kc_middle"], df["kc_lower"] = IndicatorCalculator.calculate_keltner_channels(df)

        # Market Regime
        df["market_regime"] = IndicatorCalculator.detect_market_regime(df)

        # Candlestick Patterns
        df = IndicatorCalculator.detect_candlestick_patterns(df)

        # Normalize features for better RL performance
        norm_cols = ['open_price', 'high_price', 'low_price', 'close_price', 'volume',
                    'EMA_short', 'EMA_long', 'rsi', 'adx', 'atr', 'momentum',
                    'vwap', 'obv', 'relative_volume', 'volume_roc', 'stddev_returns',
                    'kc_upper', 'kc_middle', 'kc_lower',
                    # New indicators to normalize
                    'macd', 'macd_signal', 'macd_hist', 'sar',
                    'stoch_k', 'stoch_d', 'cci', 'roc',
                    'bb_upper', 'bb_middle', 'bb_lower', 'bb_percent_b', 'volatility_ratio',
                    'mfi', 'ad_line', 'adosc', 'vwap_slope', 'obv_slope']

        for col in norm_cols:
            df[f'{col}_norm'] = self._normalize_feature(df[col], method='zscore', window=20, clip=5.0)
            #df[f'{col}_norm'] = self._normalize_feature(df[col], method='minmax', window=20)

        df = df.dropna()
        # Store processed data
        self.historical_data = df.copy()

        # Train the model if specified and sufficient data
        if self.train_on_startup and len(df) > 1000 and self.model is None:
            self._train_model(df)

        return df

    def _normalize_feature(self, series: pd.Series, method: str = 'zscore', window: int = 20, clip: float = 5.0) -> pd.Series:
        """
        Normalize a feature for PPO-based RL agent.

        Parameters:
        - series: the feature to normalize
        - method: 'zscore' or 'minmax'
        - window: rolling window size
        - clip: max absolute value to clip normalized output

        Returns:
        - Normalized and clipped pd.Series
        """
        if method == 'zscore':
            rolling_mean = series.rolling(window=window).mean()
            rolling_std = series.rolling(window=window).std()

            normalized = (series - rolling_mean) / rolling_std
        elif method == 'minmax':
            rolling_min = series.rolling(window=window).min()
            rolling_max = series.rolling(window=window).max()

            normalized = (series - rolling_min) / (rolling_max - rolling_min + 1e-8)
        else:
            raise ValueError(f"Unsupported normalization method: {method}")

        # Fill NaNs and clip outliers
        normalized = normalized.fillna(0.0)
        normalized = normalized.clip(lower=-clip, upper=clip)

        return normalized


    def linear_schedule(self, initial_value, final_value):
        """Returns a function that computes a linear schedule."""
        def schedule(progress_remaining):
            return initial_value + (final_value - initial_value) * (1 - progress_remaining)
        return schedule

    def _train_model(self, df: pd.DataFrame, timesteps: int = 100000):
        """Train the PPO model on historical data"""
        logger.info(f"Training PPO model on {len(df)} data points")

        # Create and configure the environment
        train_env = TradingEnv(df, PPOStrategy(self.pair))
        train_env.render_mode = "human"


        # Create and train the model
        model = PPO(
            "MlpPolicy",
            train_env,
            verbose=1,
            learning_rate=self.linear_schedule(0.0003, 0.00005),
            n_steps=2048,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef=0.01,
            vf_coef=0.5,
            max_grad_norm=0.5,
            tensorboard_log=f"./tensorboard_logs/{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )

        # Train the model
        try:
            model.learn(total_timesteps=timesteps)
            # Save the trained model
            model_save_path = f"./models/ppo_{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            model.save(model_save_path)
            logger.info(f"Model saved to {model_save_path}")
            self.model = model
            self.model_path = model_save_path
        except Exception as e:
            logger.error(f"Error training model: {e}")

    def reset_historical_data(self, data: Optional[pd.DataFrame] = None):
        """
        Reset the historical data to the provided DataFrame or an empty DataFrame if None.
        If the provided data has more than 1000 rows, only keep the most recent 1000.

        Args:
            data (pd.DataFrame, optional): The DataFrame to set as historical data. Defaults to None.
        """
        if data is None:
            self.historical_data = pd.DataFrame()
            logger.info("Historical data reset to empty DataFrame")
        else:
            # Limit to the most recent 1000 candles if needed
            if len(data) > 1000:
                logger.info(f"Limiting provided historical data from {len(data)} to 1000 most recent candles")
                self.historical_data = data.iloc[-1000:].reset_index(drop=True)
            else:
                self.historical_data = data.copy()
            logger.info(f"Historical data reset with {len(self.historical_data)} candles")

    def process_candle(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series, trade_manager: TradeManager, portfolio_manager: PortfolioManager):
        # Update current step and tracking variables
        self.current_price = candle['close_price']
        self.trade_manager = trade_manager
        self.portfolio_manager = portfolio_manager

        # Process the candle with the base strategy
        super().process_candle(candle, previous_candle, minusthree_candle, trade_manager, portfolio_manager)

        # Add the new candle to historical data
        new_data = pd.DataFrame([candle])
        self.historical_data = pd.concat([self.historical_data, new_data])

        # Limit historical data to 1000 most recent candles to prevent memory issues
        if len(self.historical_data) > 1000:
            logger.debug(f"Limiting historical data from {len(self.historical_data)} to 1000 most recent candles")
            self.historical_data = self.historical_data.iloc[-1000:].reset_index(drop=True)


    def should_enter_trade(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series) -> bool:
        """
        Determine if a trade should be entered based on the model's prediction.
        previous_candle and minusthree_candle parameters are required by interface but not used
        """
        # Suppress unused parameter warnings
        _ = (previous_candle, minusthree_candle)
        if self.model is None:
            return False
        observation = self._prepare_observation(candle)
        action, _ = self.model.predict(observation, deterministic=True)
        return action == 1

    def should_exit_trade(self, candle: pd.Series, previous_candle: pd.Series) -> bool:
        """
        Determine if a trade should be exited based on the model's prediction.
        previous_candle parameter is required by interface but not used
        """
        # Suppress unused parameter warning
        _ = previous_candle
        if self.model is None:
            return False
        observation = self._prepare_observation(candle)
        action, _ = self.model.predict(observation, deterministic=True)
        return action == 2

    def _prepare_observation(self, candle: pd.Series) -> np.ndarray:
        """Use TradingEnv's logic to build the observation"""
        # Create a one-row DataFrame from the candle
        df = pd.DataFrame([candle])
        env = TradingEnv(df, portfolio_manager=self.portfolio_manager, trade_manager=self.trade_manager)
        # Force current_step to 0 (only one row)
        env.current_step = 0
        obs = env._next_observation()
        return obs.reshape(1, -1)  # Required shape for model.predict


    def evaluate_performance(self, test_data: pd.DataFrame) -> Dict[str, Any]:
        """Evaluate the model's performance on test data"""
        if self.model is None:
            return {"error": "No model loaded"}

        # Create an environment with test data
        test_env = TradingEnv(test_data, PPOStrategy(pair=self.pair), initial_balance=5000)

        # Run a single episode
        obs, _ = test_env.reset()
        done = False
        total_reward = 0

        while not done:
            action_array, _ = self.model.predict(obs, deterministic=True)
            # Convert ndarray to int for step function
            action = int(action_array[0]) if hasattr(action_array, '__len__') else int(action_array)
            obs, reward, terminated, truncated, _ = test_env.step(action)
            total_reward += reward
            done = terminated or truncated

        # Calculate metrics
        final_net_worth = test_env.net_worth_history[-1]
        net_worth_series = pd.Series(test_env.net_worth_history)

        # Calculate returns
        returns = net_worth_series.pct_change().dropna()

        # Calculate metrics
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)  # Annualized
        max_drawdown = (net_worth_series / net_worth_series.cummax() - 1).min()

        return {
            "final_net_worth": final_net_worth,
            "total_return": (final_net_worth / 5000 - 1) * 100,  # Percentage
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown * 100,  # Percentage
            "total_reward": total_reward
        }

    def save_model(self, path: Optional[str] = None) -> Optional[str]:
        """Save the current model to disk"""
        if self.model is None:
            logger.error("No model to save")
            return None

        if path is None:
            path = f"./models/ppo_{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        try:
            self.model.save(path)
            logger.info(f"Model saved to {path}")
            return path
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return None

    def load_model(self, path: str) -> bool:
        """Load a model from disk"""
        try:
            self.model = PPO.load(path)
            self.model_path = path
            logger.info(f"Loaded PPO model from {path}")
            return True
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False
