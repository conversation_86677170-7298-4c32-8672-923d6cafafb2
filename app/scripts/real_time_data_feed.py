import asyncio
import pandas as pd
import time
import logging
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional
from app.exchange.kraken_websocket import KrakenWebSocket, WebSocketConnectionListener
from app.exchange.kraken_api import KrakenAPI
from app.strategy.base_strategy import BaseStrategy
from app.db.db_executor import DatabaseExecutor
from app.scripts.base_data_feed import DataFeed

logger = logging.getLogger(__name__)

INTERVAL_MAPPING = {
    "min": 1,
    "h": 60,
    "D": 1440,    # 24 * 60
    "W": 10080,   # 7 * 1440
    "M": 43200,   # 30 * 1440 (approximate month)
}

class RealTimeDataFeed(DataFeed, WebSocketConnectionListener):
    def __init__(self, pair: str, interval: str, ws_url: str, api: KrakenAPI, db: DatabaseExecutor, strategy: BaseStrategy, book_depth: int = 10, max_queue_size: int = 100):
        self.pair = pair
        self.interval = interval
        self.ws_url = ws_url
        self.db = db
        self.strategy = strategy
        self.ws = KrakenWebSocket(self.ws_url)
        self.api = api
        self.book_depth = book_depth
        self.max_queue_size = max_queue_size
        self.candle_queue = asyncio.Queue(maxsize=max_queue_size)  # Queue for enriched hourly candles with size limit
        self.connected = False
        self.subscriptions = []  # Store active subscriptions
        self._processing_lock = asyncio.Lock()  # Add a lock for synchronizing access to shared state

        # Register this class as a connection listener
        self.ws.register_connection_listener(self)

        # Calculate interval in minutes
        if isinstance(interval, int):
            self.interval_minutes = interval
        elif isinstance(interval, str):
            unit = ''.join(filter(str.isalpha, interval))  # Extract unit (e.g., 'min', 'h')
            value_str = ''.join(filter(str.isdigit, interval))  # Extract numeric part
            if not value_str or not unit:
                raise ValueError(f"Invalid interval format: {interval}")
            value = int(value_str)
            if unit not in INTERVAL_MAPPING:
                raise ValueError(f"Invalid unit in interval: {unit}")
            self.interval_minutes = value * INTERVAL_MAPPING[unit]  # Convert to minutes
        else:
            raise TypeError("Interval must be an integer or string")


    async def initialize_historical_data(self):
        """
        Fetch and store historical minute-level OHLC data from Kraken since the last stored timestamp.
        Then resample to hourly and enrich with EMAs.
        """
        # Get the last timestamp in the database
        query = "SELECT MAX(timestamp) FROM kraken_ohlc WHERE pair = %s"
        result = self.db.execute_select(query, (self.pair,))
        last_timestamp = result[0][0] if result and result[0][0] else (datetime.now(timezone.utc) - timedelta(days=30))
        since = int(last_timestamp.timestamp())

        while True:
            logger.info(f"Fetching OHLC data since {datetime.fromtimestamp(since, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
            # Fetch historical OHLC data (minute-level) from Kraken
            ohlc_data = self.api.get_ohlc(self.pair, interval=60, since=since)

            if not ohlc_data:
                logger.info("No more historical data available.")
                break  # Exit loop when no data is returned

            # Process and store the data
            new_since = None

            for candle in ohlc_data['result'][self.pair]:
                timestamp = datetime.fromtimestamp(candle[0], tz=timezone.utc)
                open_price, high_price, low_price, close_price, _, volume = map(float, candle[1:7])

                query = """
                INSERT INTO kraken_ohlc (timestamp, pair, open_price, high_price, low_price, close_price, volume)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (timestamp, pair) DO NOTHING
                """
                self.db.execute_insert(query, (timestamp, self.pair, open_price, high_price, low_price, close_price, volume))

                new_since = max(new_since or since, int(candle[0]))

            if new_since is None or new_since == since:
                break

            since = new_since

            time.sleep(3)  # Rate limiting

        # Fetch enough historical data for EMA calculations (e.g., 800 hours worth of minutes)
        await self._process_historical_candles()

    async def _process_historical_candles(self):
        """Process historical candles to initialize EMA values"""
        lookback_period = datetime.now(timezone.utc) #- timedelta(hours=800)
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp >= %s
        ORDER BY timestamp ASC
        """
        ls = list(range(1, 6))
        data = self.db.execute_select(query, (self.pair, lookback_period), float_columns=ls)

        if data:
            df = pd.DataFrame(data, columns=['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'])
            enriched_df = self.strategy.preprocess_data(df)  # Resamples to hourly and adds EMAs
            if not enriched_df.empty:
                # Only keep the most recent candles if there are too many
                if len(enriched_df) > self.max_queue_size:
                    logger.warning(f"Too many historical candles ({len(enriched_df)}), keeping only the most recent {self.max_queue_size}")
                    enriched_df = enriched_df.iloc[-self.max_queue_size:]

                for _, candle in enriched_df.iterrows():
                    try:
                        # Use put_nowait with a small timeout to avoid blocking indefinitely
                        await asyncio.wait_for(self.candle_queue.put(candle), timeout=0.1)
                    except asyncio.TimeoutError:
                        logger.warning("Queue is full, dropping older candles to make room")
                        # If queue is full, make room by getting an item
                        try:
                            if not self.candle_queue.empty():
                                self.candle_queue.get_nowait()
                                self.candle_queue.task_done()
                            await self.candle_queue.put(candle)
                        except Exception as e:
                            logger.error(f"Error managing candle queue: {e}")

                logger.info(f"Processed {len(enriched_df)} historical candles, queue size: {self.candle_queue.qsize()}/{self.max_queue_size}")

    async def connect(self):
        if not self.connected:
            await self.initialize_historical_data()
            await self.ws.start()
            await self._initialize_subscriptions()
            self.connected = True

    async def _initialize_subscriptions(self):
        """Initialize all required subscriptions"""
        # Store subscription details for reconnection
        ohlc_subscription = {
            'type': 'ohlc',
            'pair': self.pair,
            'interval': self.interval
        }
        book_subscription = {
            'type': 'book',
            'pair': self.pair,
            'depth': self.book_depth
        }

        # Add OHLC subscription if not already subscribed
        if ohlc_subscription not in self.subscriptions:
            self.subscriptions.append(ohlc_subscription)
            await self.ws.subscribe_ohlc([self.pair], self.interval, self.on_ohlc_data)
            logger.info(f"Initialized OHLC subscription for {self.pair} with interval {self.interval}")

        # Add order book subscription if not already subscribed
        if book_subscription not in self.subscriptions:
            self.subscriptions.append(book_subscription)
            await self.ws.subscribe_book([self.pair], self.book_depth, self.on_book_data)
            logger.info(f"Initialized order book subscription for {self.pair} with depth {self.book_depth}")

    async def reestablish_subscriptions(self):
        """Reestablish all stored subscriptions after reconnection"""
        logger.info("Reestablishing subscriptions after reconnection")
        for subscription in self.subscriptions:
            if subscription['type'] == 'ohlc':
                await self.ws.subscribe_ohlc(
                    [subscription['pair']],
                    subscription['interval'],
                    self.on_ohlc_data
                )
                logger.info(f"Re-subscribed to OHLC for {subscription['pair']} with interval {subscription['interval']}")
            elif subscription['type'] == 'book':
                await self.ws.subscribe_book(
                    [subscription['pair']],
                    subscription['depth'],
                    self.on_book_data
                )
                logger.info(f"Re-subscribed to order book for {subscription['pair']} with depth {subscription['depth']}")
            # Add handlers for other subscription types as needed

    async def on_websocket_connected(self):
        """Implementation of WebSocketConnectionListener interface"""
        logger.info("WebSocket connected/reconnected")
        self.connected = True
        # Reestablish subscriptions when connection is restored
        await self.reestablish_subscriptions()

    async def on_websocket_disconnected(self):
        """Implementation of WebSocketConnectionListener interface"""
        logger.warning("WebSocket disconnected, waiting for reconnection")
        self.connected = False

    def on_ohlc_data(self, data):
        """
        Handle incoming OHLC data from the WebSocket.
        This is a synchronous callback that delegates to an async processor.
        """
        asyncio.create_task(self._process_ohlc_data_safe(data))

    async def _process_ohlc_data_safe(self, data):
        """
        Wrapper that ensures only one _process_ohlc_data runs at a time.
        """
        async with self._processing_lock:
            await self._process_ohlc_data(data)

    async def _process_ohlc_data(self, data):
        """
        Process OHLC data asynchronously.
        """
        if isinstance(data, list) and len(data) >= 2 and isinstance(data[1], list) and len(data[1]) == 10:
            ohlc_data = data[1]  # Extract the nested OHLC list
            start_time = datetime.fromtimestamp(float(ohlc_data[0]), tz=timezone.utc)
            candle = {
                'timestamp': start_time,
                'open_price': float(ohlc_data[2]),
                'high_price': float(ohlc_data[3]),
                'low_price': float(ohlc_data[4]),
                'close_price': float(ohlc_data[5]),
                'volume': float(ohlc_data[7])
            }
            logger.debug(f"Received OHLC data for timestamp: {candle['timestamp']}")

            # Insert or update the candle in the database
            query = """
            INSERT INTO kraken_ohlc (timestamp, pair, open_price, high_price, low_price, close_price, volume)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (timestamp, pair) DO UPDATE
            SET open_price = EXCLUDED.open_price,
                high_price = EXCLUDED.high_price,
                low_price = EXCLUDED.low_price,
                close_price = EXCLUDED.close_price,
                volume = EXCLUDED.volume
            """
            try:
                self.db.execute_insert(query, (
                    candle['timestamp'], self.pair, candle['open_price'],
                    candle['high_price'], candle['low_price'], candle['close_price'], candle['volume']
                ))
                logger.debug(f"Upserted candle for {candle['timestamp']} into database")

                # Check if we need to retrain the PPO model (if strategy is PPOStrategy)
                if hasattr(self.strategy, '__class__') and self.strategy.__class__.__name__ == 'PPOStrategy':
                    await self._check_ppo_training_schedule(candle['timestamp'])

            except Exception as e:
                logger.error(f"Failed to upsert candle for {candle['timestamp']}: {e}")

            # Check if this is a new interval
            if not hasattr(self, 'last_start_time') or start_time > self.last_start_time:
                if hasattr(self, 'last_start_time'):
                    # Previous candle is complete, enrich it
                    await self._process_complete_candle()
                self.last_start_time = start_time
        else:
            logger.warning(f"Received invalid OHLC data: {data}")

    async def _check_ppo_training_schedule(self, current_time: datetime):
        """
        Check if it's time to retrain the PPO model based on the 7-day schedule.

        Args:
            current_time (datetime): The current timestamp
        """
        # Skip if strategy is not PPOStrategy
        if not hasattr(self.strategy, '__class__') or self.strategy.__class__.__name__ != 'PPOStrategy':
            return

        # Import PPOStrategy for type checking
        from app.strategy.ppo_strategy import PPOStrategy

        # Type cast for IDE type checking
        if not isinstance(self.strategy, PPOStrategy):
            return

        ppo_strategy = self.strategy  # Now IDE knows this is a PPOStrategy

        # Initialize last_training_time if not set
        if not hasattr(ppo_strategy, 'last_training_time') or ppo_strategy.last_training_time is None:
            ppo_strategy.last_training_time = current_time
            logger.info(f"Initialized PPO strategy last_training_time to {current_time}")
            return

        # Check if 7 days have passed since the last training
        days_since_training = (current_time - ppo_strategy.last_training_time).days
        retrain_interval = getattr(ppo_strategy, 'retrain_interval_days', 7)  # Default to 7 if not set

        if days_since_training >= retrain_interval:
            logger.info(f"Retraining interval reached ({retrain_interval} days). Initiating PPO model retraining.")

            # Retrieve all historical data from the database (excluding the most recent candle)
            try:
                # Get all historical data up to but not including the current candle
                complete_data = await self._get_all_historical_data(end_time=current_time)

                if len(complete_data) > 0:
                    # Train the model with complete historical data
                    logger.info(f"Training PPO model with {len(complete_data)} historical candles")
                    if hasattr(ppo_strategy, '_train_model') and callable(getattr(ppo_strategy, '_train_model')):
                        ppo_strategy._train_model(complete_data)

                        # After training, reset the strategy's historical_data to only contain the most recent 1000 candles
                        if hasattr(ppo_strategy, 'reset_historical_data') and callable(getattr(ppo_strategy, 'reset_historical_data')):
                            ppo_strategy.reset_historical_data(complete_data)

                        # Update the last training time
                        ppo_strategy.last_training_time = current_time
                        logger.info(f"PPO model training complete. Next training scheduled in {retrain_interval} days.")
                    else:
                        logger.error("PPO strategy does not have a _train_model method")
                else:
                    logger.warning("No historical data available for PPO model training")
            except Exception as e:
                logger.error(f"Error during PPO model training: {e}")

    async def _get_all_historical_data(self, end_time: datetime) -> pd.DataFrame:
        """
        Retrieve all historical data from the database up to end_time.

        Args:
            end_time (datetime): The end time for the historical data query

        Returns:
            pd.DataFrame: Preprocessed historical data
        """
        # Query to get all historical data up to but not including the current candle
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp < %s
        ORDER BY timestamp ASC
        """

        try:
            logger.info(f"Retrieving all historical data for {self.pair} up to {end_time}")
            data = self.db.execute_select(query, (self.pair, end_time), float_columns=list(range(1, 6)))

            if not data:
                logger.warning("No historical data found in database")
                return pd.DataFrame()

            df = pd.DataFrame(data, columns=['timestamp', 'open_price', 'high_price',
                                           'low_price', 'close_price', 'volume'])

            # Preprocess the data using the strategy's preprocess_data method
            processed_df = self.strategy.preprocess_data(df)
            logger.info(f"Retrieved and preprocessed {len(processed_df)} candles from database")
            return processed_df

        except Exception as e:
            logger.error(f"Error retrieving historical data: {e}")
            return pd.DataFrame()

    def on_book_data(self, data):
        """
        Handle incoming order book data from the WebSocket.
        This is a synchronous callback that delegates to an async processor.
        """
        asyncio.create_task(self._process_book_data_safe(data))

    async def _process_book_data_safe(self, data):
        """
        Wrapper that ensures only one _process_book_data runs at a time.
        """
        async with self._processing_lock:
            await self._process_book_data(data)

    async def _process_book_data(self, data):
        """
        Process order book data asynchronously.
        """
        try:
            # Validate data structure
            if not isinstance(data, dict) or 'symbol' not in data or 'type' not in data:
                logger.warning(f"Received invalid order book data format: {data}")
                return

            # Parse the order book data
            book_type = data.get('type')
            symbol = data.get('symbol')
            timestamp = datetime.now(timezone.utc)

            if data.get('timestamp'):
                try:
                    # Try to parse the timestamp from the data if available
                    timestamp_str = data.get('timestamp')
                    if timestamp_str:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    # If timestamp parsing fails, use current time
                    pass

            bids = data.get('bids', [])
            asks = data.get('asks', [])
            checksum = data.get('checksum')

            # Only store snapshots (initial book state) and not incremental updates
            # You might want to modify this to handle updates differently
            if book_type == 'snapshot' or not hasattr(self, f'last_book_update_{symbol}'):
                # Store the timestamp of last update for this symbol
                setattr(self, f'last_book_update_{symbol}', timestamp)

                # Convert bids and asks to JSON strings
                bids_json = json.dumps(bids)
                asks_json = json.dumps(asks)

                # Store in database
                query = """
                INSERT INTO order_book_snapshots (pair, snapshot_time, bids, asks, checksum)
                VALUES (%s, %s, %s, %s, %s)
                """
                self.db.execute_insert(query, (
                    symbol,
                    timestamp,
                    bids_json,
                    asks_json,
                    checksum
                ))
                logger.debug(f"Stored order book snapshot for {symbol} at {timestamp}")
            else:
                # For incremental updates, we can implement a throttling mechanism
                # to avoid too frequent database writes
                last_update = getattr(self, f'last_book_update_{symbol}')
                update_interval = timedelta(seconds=30)  # Configure update frequency

                if timestamp - last_update >= update_interval:
                    # Convert bids and asks to JSON strings
                    bids_json = json.dumps(bids)
                    asks_json = json.dumps(asks)

                    # Store in database
                    query = """
                    INSERT INTO order_book_snapshots (pair, snapshot_time, bids, asks, checksum)
                    VALUES (%s, %s, %s, %s, %s)
                    """
                    self.db.execute_insert(query, (
                        symbol,
                        timestamp,
                        bids_json,
                        asks_json,
                        checksum
                    ))
                    setattr(self, f'last_book_update_{symbol}', timestamp)
                    logger.debug(f"Stored order book update for {symbol} at {timestamp}")

        except Exception as e:
            logger.error(f"Error processing order book data: {e}")

    async def _process_complete_candle(self):
        """Process a complete candle interval"""
        lookback_candles = 1000
        lookback_time = self.last_start_time - timedelta(minutes=float(self.interval_minutes) * (lookback_candles - 1))
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp >= %s AND timestamp <= %s
        ORDER BY timestamp ASC
        """
        try:
            data = self.db.execute_select(query, (self.pair, lookback_time, self.last_start_time))
            logger.debug(f"Fetched {len(data)} candles for processing")
            if len(data) >= lookback_candles:
                df = pd.DataFrame(data, columns=['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'])
                enriched_df = self.strategy.preprocess_data(df)

                # If this is a PPOStrategy, ensure its historical_data is properly maintained
                if hasattr(self.strategy, '__class__') and self.strategy.__class__.__name__ == 'PPOStrategy':
                    # Import PPOStrategy for type checking
                    from app.strategy.ppo_strategy import PPOStrategy

                    # Type cast for IDE type checking
                    if isinstance(self.strategy, PPOStrategy):
                        # If the strategy's historical_data is empty, initialize it with our processed data
                        if hasattr(self.strategy, 'historical_data') and self.strategy.historical_data.empty and not enriched_df.empty:
                            logger.info("Initializing PPO strategy's historical_data with processed candles")
                            if hasattr(self.strategy, 'reset_historical_data') and callable(getattr(self.strategy, 'reset_historical_data')):
                                self.strategy.reset_historical_data(enriched_df)

                if not enriched_df.empty:
                    latest_candle = enriched_df.iloc[-1]

                    try:
                        # Check if queue is almost full
                        if self.candle_queue.qsize() >= self.max_queue_size * 0.9:
                            logger.warning(f"Candle queue is almost full ({self.candle_queue.qsize()}/{self.max_queue_size}), removing oldest candle")
                            # Remove oldest candle if queue is almost full
                            if not self.candle_queue.empty():
                                self.candle_queue.get_nowait()
                                self.candle_queue.task_done()

                        # Use put_nowait with a small timeout to avoid blocking indefinitely
                        await asyncio.wait_for(self.candle_queue.put(latest_candle), timeout=0.1)
                        logger.info(f"Enriched {self.interval_minutes}-minute candle generated for {latest_candle['timestamp']}, queue size: {self.candle_queue.qsize()}/{self.max_queue_size}")
                    except asyncio.TimeoutError:
                        logger.warning("Queue is full when adding latest candle, dropping oldest candle")
                        # If queue is full, make room by getting an item
                        try:
                            if not self.candle_queue.empty():
                                self.candle_queue.get_nowait()
                                self.candle_queue.task_done()
                            await self.candle_queue.put(latest_candle)
                            logger.info(f"Added latest candle after making room, queue size: {self.candle_queue.qsize()}/{self.max_queue_size}")
                        except Exception as e:
                            logger.error(f"Error managing candle queue: {e}")
                else:
                    logger.warning(f"No enriched data generated for {self.interval_minutes}-minute interval")
            else:
                logger.warning(f"Not enough candles ({len(data)} < {lookback_candles}) for strategy processing")
        except Exception as e:
            logger.error(f"Error processing {self.interval_minutes}-minute candle for {self.last_start_time}: {e}")

    async def get_next_candle(self) -> Optional[pd.Series]:
        """
        Get the next enriched candle from the queue using a polling approach.
        If disconnected, attempt to reconnect.
        Returns None if no more candles are expected (e.g., after a disconnect that cannot be recovered).
        """
        # Ensure connection is established
        if not self.connected:
            logger.warning("WebSocket not connected, attempting to reconnect")
            try:
                await self.connect()
            except Exception as e:
                logger.error(f"Failed to reconnect: {e}")
                return None  # Signal that no more candles are expected

        # Poll the queue instead of blocking with a timeout
        while self.connected:
            try:
                # Check if there's a candle available
                if not self.candle_queue.empty():
                    candle = await self.candle_queue.get()
                    queue_size = self.candle_queue.qsize()
                    logger.info(f"Retrieved candle for {candle['timestamp']} from queue. Queue size: {queue_size}/{self.max_queue_size}")
                    # Mark the task as done to properly track queue size
                    self.candle_queue.task_done()
                    return candle

                # Wait a bit before checking again
                await asyncio.sleep(60)  # Check every minute

                # Log status periodically (e.g., hourly)
                now = datetime.now(timezone.utc)
                if not hasattr(self, 'last_status_log') or (now - self.last_status_log).total_seconds() > 3600:
                    # Calculate time until next expected candle
                    next_hour = (now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1))
                    minutes_until_next = int((next_hour - now).total_seconds() / 60)

                    logger.debug(f"Waiting for next candle (expected in ~{minutes_until_next} minutes), connection is active")
                    self.last_status_log = now
            except Exception as e:
                logger.error(f"Error while polling for candle: {e}")
                await asyncio.sleep(10)  # Brief pause before retrying after an error

        logger.warning("Connection lost while waiting for candle")
        return None  # Connection lost

    async def get_latest_order_book(self) -> Optional[Dict]:
        """
        Get the latest order book snapshot from the database.
        """
        query = """
        SELECT pair, snapshot_time, bids, asks, checksum
        FROM order_book_snapshots
        WHERE pair = %s
        ORDER BY snapshot_time DESC
        LIMIT 1
        """
        try:
            data = self.db.execute_select(query, (self.pair,))
            if data:
                row = data[0]
                return {
                    'pair': row[0],
                    'snapshot_time': row[1],
                    'bids': json.loads(row[2]),
                    'asks': json.loads(row[3]),
                    'checksum': row[4]
                }
            return None
        except Exception as e:
            logger.error(f"Error retrieving latest order book: {e}")
            return None

    async def get_order_book_history(self, start_time: datetime, end_time: datetime) -> List[Dict]:
        """
        Get historical order book snapshots for a specific time range.
        """
        query = """
        SELECT pair, snapshot_time, bids, asks, checksum
        FROM order_book_snapshots
        WHERE pair = %s AND snapshot_time BETWEEN %s AND %s
        ORDER BY snapshot_time ASC
        """
        try:
            data = self.db.execute_select(query, (self.pair, start_time, end_time))
            results = []
            for row in data:
                results.append({
                    'pair': row[0],
                    'snapshot_time': row[1],
                    'bids': json.loads(row[2]),
                    'asks': json.loads(row[3]),
                    'checksum': row[4]
                })
            return results
        except Exception as e:
            logger.error(f"Error retrieving order book history: {e}")
            return []

    async def disconnect(self):
        if self.connected:
            await self.ws.disconnect()

    async def health_check(self):
        """
        Periodically check the health of the WebSocket connection.
        Can be run as a background task.
        """
        while True:
            if not self.connected or not self.ws.ws or not self.ws.is_connected():
                logger.warning("Health check: WebSocket connection lost, reconnecting...")
                await self.connect()
            await asyncio.sleep(60)  # Check every minute