import asyncio
import pandas as pd
import time
import logging
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Callable, Set, Union
from app.exchange.kraken_websocket import KrakenWebSocketV2, State, ChannelType
from app.exchange.kraken_api import KrakenAP<PERSON>
from app.strategy.base_strategy import BaseStrategy
from app.db.db_executor import DatabaseExecutor
from app.scripts.base_data_feed import DataFeed

logger = logging.getLogger(__name__)

INTERVAL_MAPPING = {
    "min": 1,
    "h": 60,
    "D": 1440,    # 24 * 60
    "W": 10080,   # 7 * 1440
    "M": 43200,   # 30 * 1440 (approximate month)
}

# Mapping of interval minutes to Kraken v2 interval strings
KRAKEN_V2_INTERVALS = {
    1: "1m",      # 1 minute
    5: "5m",      # 5 minutes
    15: "15m",    # 15 minutes
    30: "30m",    # 30 minutes
    60: "1h",     # 1 hour
    240: "4h",    # 4 hours
    1440: "1d",   # 1 day
    10080: "1w",  # 1 week
    21600: "1M"   # 1 month (approximate)
}


class RealTimeDataFeed(DataFeed):
    def __init__(self, pair: str, interval: str, api: KrakenAPI, db: DatabaseExecutor, strategy: BaseStrategy, book_depth: int = 10, max_queue_size: int = 100):
        self.pair = pair
        self.interval = interval
        self.db = db
        self.strategy = strategy
        self.ws = KrakenWebSocketV2(target_symbol=self.pair)
        self.api = api
        self.book_depth = book_depth
        self.max_queue_size = max_queue_size
        self.candle_queue = asyncio.Queue(maxsize=max_queue_size)
        # In-memory order book state
        self.order_book = {'bids': {}, 'asks': {}}
        self.subscriptions = []
        self._processing_lock = asyncio.Lock()
        self._connection_event = asyncio.Event()
        self.connected = False

        # Enhanced subscription tracking
        self._desired_subscriptions = {}  # What we want to be subscribed to
        self._active_subscriptions = {}   # What we're actually subscribed to
        self._pending_subscriptions = {}  # What we're waiting for confirmation on
        self._subscription_callbacks = {}  # Callback mapping

        # Connection state
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5

        # Register as connection listener
        self.ws.connection_listeners.add(self)

        # Parse interval
        self.interval_minutes = self._parse_interval(interval)
        self.kraken_interval = KRAKEN_V2_INTERVALS.get(
            self.interval_minutes, "1h")
        logger.info(
            f"Using interval: {self.kraken_interval} (converted from {interval})")

    async def fetch_order_book_features(self, start_time, end_time):
        """
        Fetch and aggregate order book snapshots between start_time and end_time,
        compute order book features per candle interval, and return a DataFrame
        with timestamps and features suitable for merging with OHLC data.
        """
        try:
            # Fetch order book snapshots from DB
            order_book_snapshots = await self.get_order_book_history(start_time, end_time)
            if not order_book_snapshots:
                return pd.DataFrame()

            # Convert snapshots to DataFrame
            records = []
            for snapshot in order_book_snapshots:
                timestamp = snapshot['snapshot_time']
                bids = snapshot.get('bids', [])
                asks = snapshot.get('asks', [])
                records.append({
                    'timestamp': timestamp,
                    'bids': bids,
                    'asks': asks
                })
            df_snapshots = pd.DataFrame(records)
            if df_snapshots.empty:
                return pd.DataFrame()

            # Aggregate snapshots by candle interval (e.g., hourly)
            # For simplicity, group by floor of timestamp to interval
            df_snapshots['interval_start'] = df_snapshots['timestamp'].dt.floor(self.interval)

            # Compute features per interval
            feature_rows = []
            for interval_start, group in df_snapshots.groupby('interval_start'):
                # Aggregate bids and asks across group
                agg_bids = []
                agg_asks = []
                for bids_list in group['bids']:
                    agg_bids.extend(bids_list)
                for asks_list in group['asks']:
                    agg_asks.extend(asks_list)

                # Compute features using PPO strategy method
                features = self.strategy.calculate_orderbook_features({
                    'bids': agg_bids,
                    'asks': agg_asks
                })
                features['timestamp'] = interval_start
                feature_rows.append(features)

            df_features = pd.DataFrame(feature_rows)
            return df_features

        except Exception as e:
            logger.error(f"Error fetching order book features: {e}")
            return pd.DataFrame()
    def _parse_interval(self, interval: str) -> int:
        """Parse interval string into minutes"""
        if isinstance(interval, int):
            return interval

        # Extract unit (e.g., 'min', 'h')
        unit = ''.join(filter(str.isalpha, interval))
        value_str = ''.join(filter(str.isdigit, interval))

        if not value_str or not unit:
            raise ValueError(f"Invalid interval format: {interval}")

        value = int(value_str)

        if unit not in INTERVAL_MAPPING:
            raise ValueError(f"Invalid unit in interval: {unit}")
        return value * INTERVAL_MAPPING[unit]  # Convert to minutes

    async def initialize_historical_data(self):
        """
        Fetch and store historical minute-level OHLC data from Kraken since the last stored timestamp.
        Then resample to hourly and enrich with EMAs.
        """
        # Get the last timestamp in the database
        query = "SELECT MAX(timestamp) FROM kraken_ohlc WHERE pair = %s"
        result = self.db.execute_select(query, (self.pair,))
        last_timestamp = result[0][0] if result and result[0][0] else (
            datetime.now(timezone.utc) - timedelta(days=30))
        since = int(last_timestamp.timestamp())

        while True:
            logger.info(
                f"Fetching OHLC data since {datetime.fromtimestamp(since, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
            # Fetch historical OHLC data (minute-level) from Kraken
            ohlc_data = self.api.get_ohlc(
                self.pair, interval=self.interval_minutes, since=since)

            if not ohlc_data:
                logger.info("No more historical data available.")
                break  # Exit loop when no data is returned

            # Process and store the data
            new_since = None

            for candle in ohlc_data['result'][self.pair]:
                timestamp = datetime.fromtimestamp(candle[0], tz=timezone.utc)
                open_price, high_price, low_price, close_price, _, volume = map(
                    float, candle[1:7])

                query = """
                INSERT INTO kraken_ohlc (timestamp, pair, open_price, high_price, low_price, close_price, volume)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (timestamp, pair) DO NOTHING
                """
                self.db.execute_insert(
                    query, (timestamp, self.pair, open_price, high_price, low_price, close_price, volume))

                new_since = max(new_since or since, int(candle[0]))

            if new_since is None or new_since == since:
                break

            since = new_since

            time.sleep(3)  # Rate limiting

        # Fetch enough historical data for EMA calculations (e.g., 800 hours worth of minutes)
        await self._process_historical_candles()

    async def _process_historical_candles(self):
        """Process historical candles to initialize EMA values"""
        lookback_period = datetime.now(timezone.utc) - timedelta(hours=800)
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp >= %s
        ORDER BY timestamp ASC
        """
        float_columns = list(range(1, 6))
        historical_candles = self.db.execute_select(
            query, (self.pair, lookback_period), float_columns=float_columns)

        if historical_candles:
            df = pd.DataFrame(historical_candles, columns=[
                              'timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'])
            enriched_df = self.strategy.preprocess_data(df)
            if not enriched_df.empty:
                # Only keep the most recent candles if there are too many
                if len(enriched_df) > self.max_queue_size:
                    logger.warning(
                        f"Too many historical candles ({len(enriched_df)}), keeping only the most recent {self.max_queue_size}")
                    enriched_df = enriched_df.iloc[-self.max_queue_size:]

                # for _, candle in enriched_df.iterrows():
                #     try:
                #         # Use put_nowait with a small timeout to avoid blocking indefinitely
                #         await asyncio.wait_for(self.candle_queue.put(candle), timeout=0.1)
                #     except asyncio.TimeoutError:
                #         logger.warning(
                #             "Queue is full, dropping older candles to make room")
                #         # If queue is full, make room by getting an item
                #         try:
                #             if not self.candle_queue.empty():
                #                 self.candle_queue.get_nowait()
                #                 self.candle_queue.task_done()
                #             await self.candle_queue.put(candle)
                #         except Exception as e:
                #             logger.error(f"Error managing candle queue: {e}")

                logger.info(
                    f"Processed {len(enriched_df)} historical candles for {self.pair}, queue size: {self.candle_queue.qsize()}/{self.max_queue_size}")

    async def connect(self):
        if not self.ws.is_public_connected():
            logger.info("Connecting to WebSocket...")
            await self.initialize_historical_data()
            try:
                await self.ws.connect_public()
            except Exception as e:
                logger.error(f"Connection failed: {e}")
                # Connection will be handled by reconnection logic
            finally:
                # Initialize subscriptions regardless of connection status
                await self._initialize_subscriptions()

    async def _initialize_subscriptions(self):
        """Initialize all required subscriptions"""
        # Define what we want to subscribe to
        self._desired_subscriptions = {
            f"ohlc-{self.pair}": {
                'channel': ChannelType.OHLC,
                'symbols': [self.pair],
                'callback': self.on_ohlc_data,
                'params': {'interval': self.interval_minutes}
            },
            f"book-{self.pair}": {
                'channel': ChannelType.BOOK,
                'symbols': [self.pair],
                'callback': self.on_book_data,
                'params': {'depth': self.book_depth}
            }
        }

        # Subscribe to everything we want
        await self._subscribe_to_desired()

    async def _subscribe_to_desired(self):
        """Subscribe to all desired subscriptions that aren't already active"""
        for sub_key, sub_config in self._desired_subscriptions.items():
            if sub_key not in self._active_subscriptions and sub_key not in self._pending_subscriptions:
                await self._subscribe_single(sub_key, sub_config)

    async def _subscribe_single(self, sub_key: str, sub_config: dict):
        """Subscribe to a single channel with proper error handling"""
        try:
            # Mark as pending
            self._pending_subscriptions[sub_key] = sub_config

            # Store callback for later use
            self._subscription_callbacks[sub_key] = sub_config['callback']

            # Perform the subscription
            await self.ws.subscribe(
                channel=sub_config['channel'],
                symbols=sub_config['symbols'],
                callback=sub_config['callback'],
                **sub_config.get('params', {})
            )

            logger.info(f"Subscription request sent for {sub_key}")

        except websockets.exceptions.WebSocketException as e:
            # Remove from pending on error
            self._pending_subscriptions.pop(sub_key, None)

            if "already subscribed" in str(e).lower():
                # Mark as active if already subscribed
                self._active_subscriptions[sub_key] = sub_config
                logger.info(f"Already subscribed to {sub_key}")
            else:
                logger.error(f"Error subscribing to {sub_key}: {e}")
                # Could implement retry logic here
            await self.ws.subscribe(
                channel=ChannelType.OHLC,
                symbols=[self.pair],
                callback=self.on_ohlc_data,
                interval=self.interval_minutes
            )
            self._active_subscriptions.add(f"ohlc-{self.pair}")
            logger.info(
                f"Subscribed to OHLC for {self.pair} with interval {self.kraken_interval}")

            # Subscribe to order book
            await self.ws.subscribe(
                channel=ChannelType.BOOK,
                symbols=[self.pair],
                callback=self.on_book_data,
                depth=self.book_depth
            )
            self._active_subscriptions.add(f"book-{self.pair}")
            logger.info(
                f"Subscribed to order book for {self.pair} with depth {self.book_depth}")

        except Exception as e:
            logger.error(f"Unexpected error subscribing to {sub_key}: {e}")

    async def on_websocket_connected(self):
        """Handle WebSocket connection establishment"""
        self.connected = True
        self._reconnect_attempts = 0
        logger.info("WebSocket connection established.")
        # Signal that the connection is ready
        self._connection_event.set()

        # Clear pending subscriptions (they're invalid now)
        self._pending_subscriptions.clear()

        # Resubscribe to everything we want
        await self._subscribe_to_desired()

    async def on_websocket_disconnected(self):
        """Handle WebSocket disconnection"""
        self.connected = False
        logger.warning("WebSocket disconnected, waiting for reconnection")
        self._connection_event.clear()

        # Clear active subscriptions (they're no longer valid)
        self._active_subscriptions.clear()
        self._pending_subscriptions.clear()

    def on_subscription_confirmed(self, sub_key: str):
        """Handle subscription confirmation from WebSocket"""
        if sub_key in self._pending_subscriptions:
            # Move from pending to active
            self._active_subscriptions[sub_key] = self._pending_subscriptions.pop(
                sub_key)
            logger.info(f"Received subscription confirmation for {sub_key}")

    def on_subscription_error(self, sub_key: str, error: str):
        """Handle subscription error from WebSocket"""
        if sub_key in self._pending_subscriptions:
            config = self._pending_subscriptions.pop(sub_key)
            logger.error(f"Subscription error for {sub_key}: {error}")

            # Could implement retry logic here
            # For now, just log it

    async def add_subscription(self, sub_key: str, channel: ChannelType, symbols: list[str], callback: Callable, **params):
        """Add a new subscription at runtime"""
        sub_config = {
            'channel': channel,
            'symbols': symbols,
            'callback': callback,
            'params': params
        }

        # Add to desired subscriptions
        self._desired_subscriptions[sub_key] = sub_config

        # Subscribe immediately if connected
        if self.connected:
            await self._subscribe_single(sub_key, sub_config)

    async def remove_subscription(self, sub_key: str):
        """Remove a subscription at runtime"""
        # Remove from desired subscriptions
        self._desired_subscriptions.pop(sub_key, None)

        # Unsubscribe if active
        if sub_key in self._active_subscriptions:
            config = self._active_subscriptions.pop(sub_key)
            try:
                await self.ws.unsubscribe(
                    channel=config['channel'],
                    symbols=config['symbols']
                )
                logger.info(f"Unsubscribed from {sub_key}")
            except websockets.exceptions.WebSocketException as e:
                logger.error(
                    f"WebSocket error unsubscribing from {sub_key}: {e}")
            except Exception as e:
                logger.error(f"Error unsubscribing from {sub_key}: {e}")

        # Clean up other tracking
        self._pending_subscriptions.pop(sub_key, None)
        self._subscription_callbacks.pop(sub_key, None)

    async def wait_for_connection(self, timeout: float = 30.0):
        """Wait for WebSocket connection to be established"""
        try:
            await asyncio.wait_for(self._connection_event.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            logger.error(f"Connection timeout after {timeout} seconds")
            return False

    def get_subscription_status(self) -> dict[str, str]:
        """Get current subscription status for debugging"""
        status = {}
        for sub_key in self._desired_subscriptions:
            if sub_key in self._active_subscriptions:
                status[sub_key] = "active"
            elif sub_key in self._pending_subscriptions:
                status[sub_key] = "pending"
            else:
                status[sub_key] = "inactive"
        return status

    def on_ohlc_data(self, ohlc_message):
        """
        Handle incoming OHLC data from the WebSocket.
        This is a synchronous callback that delegates to an async processor.

        Args:
            ohlc_message: The OHLC data message received from the WebSocket
        """
        asyncio.create_task(self._process_ohlc_data_safe(ohlc_message))

    async def _process_ohlc_data_safe(self, ohlc_message):
        """
        Wrapper that ensures only one _process_ohlc_data runs at a time.

        Args:
            ohlc_message: The OHLC data message to be processed
        """
        async with self._processing_lock:
            await self._process_ohlc_data(ohlc_message)

    async def _process_ohlc_data(self, ohlc_data: dict):
        """Process OHLC data with v2 format handling

        Args:
            ohlc_data (dict): The OHLC data received from the WebSocket
        """
        try:
            if ohlc_data.get("channel") == "ohlc":
                # Check for success/error fields in the response
                if "success" in ohlc_data and not ohlc_data["success"]:
                    logger.error(
                        f"OHLC subscription error: {ohlc_data.get('error', 'Unknown error')}")
                    return

                # Extract OHLC data from the message
                # Kraken v2 OHLC messages have 'data' key with list of updates
                ohlc = ohlc_data.get("data", [])
                if not ohlc:
                    logger.warning("Received empty OHLC data")
                    return

                # Handle both single update and batch updates
                updates = [ohlc] if not isinstance(ohlc, list) else ohlc

                # Process each update
                for ohlc_update in updates:
                    try:
                        # Get the symbol - it might be in different places
                        symbol = ohlc_update.get('symbol', self.pair)
                        if symbol and symbol != self.pair:
                            continue

                        # Get timestamp - it might be in different formats
                        timestamp_value = None

                        # Try different possible timestamp fields
                        if 'times' in ohlc_update:
                            timestamp_value = ohlc_update['time']
                        elif 'timestamp' in ohlc_update:
                            timestamp_value = ohlc_update['timestamp']

                        if not timestamp_value:
                            logger.warning(
                                f"No timestamp found in OHLC data: {ohlc_update}")
                            continue

                        # Handle different timestamp formats
                        try:
                            if isinstance(timestamp_value, (int, float)):
                                # Handle Unix timestamp (in seconds or milliseconds)
                                # Kraken OHLC timestamps are in seconds with decimal fractions
                                # Defensive: if timestamp_value is very small, treat as seconds
                                if timestamp_value > 1e12:  # Likely in milliseconds
                                    timestamp_value = timestamp_value / 1000.0
                                elif timestamp_value < 1e9:  # Too small, likely seconds but very old date
                                    logger.warning(
                                        f"Unusually small timestamp value: {timestamp_value}")
                                start_time = datetime.fromtimestamp(
                                    timestamp_value, tz=timezone.utc)
                            elif isinstance(timestamp_value, str):
                                # Handle ISO format string
                                if timestamp_value.endswith('Z'):
                                    timestamp_value = timestamp_value[:-
                                                                      1] + '+00:00'
                                start_time = datetime.fromisoformat(
                                    timestamp_value)
                                if start_time.tzinfo is None:
                                    start_time = start_time.replace(
                                        tzinfo=timezone.utc)
                            else:
                                logger.error(
                                    f"Unsupported timestamp format: {timestamp_value}")
                                continue

                            logger.debug(
                                f"Parsed timestamp: {start_time} (type: {type(timestamp_value).__name__})")

                        except (ValueError, TypeError) as e:
                            logger.error(
                                f"Error parsing timestamp {timestamp_value}: {e}")
                            continue

                        # Extract OHLCV values - handle different field names
                        candle = {
                            'timestamp': start_time,
                            'open_price': float(ohlc_update.get('open', ohlc_update.get('open_price', 0))),
                            'high_price': float(ohlc_update.get('high', ohlc_update.get('high_price', 0))),
                            'low_price': float(ohlc_update.get('low', ohlc_update.get('low_price', 0))),
                            'close_price': float(ohlc_update.get('close', ohlc_update.get('close_price', 0))),
                            'volume': float(ohlc_update.get('volume', ohlc_update.get('volume_24h', 0)))
                        }

                        logger.debug(f"Received {self.kraken_interval} OHLC data for {candle['timestamp']}: "
                                     f"O={candle['open_price']} H={candle['high_price']} "
                                     f"L={candle['low_price']} C={candle['close_price']} "
                                     f"V={candle['volume']}")

                        # Defensive check for None candle or missing close_price
                        if candle is None or candle.get('close_price', None) is None:
                            logger.warning(
                                f"Skipping candle with missing close_price: {candle}")
                            continue

                        # Defensive check for close_price being None or NaN
                        close_price = candle.get('close_price')
                        if close_price is None or (isinstance(close_price, float) and (close_price != close_price)):
                            logger.warning(
                                f"Skipping candle with invalid close_price: {candle}")
                            continue

                        # Defensive check for candle being None or missing close_price attribute
                        if candle is None or ('close_price' not in candle or candle['close_price'] is None):
                            logger.warning(
                                f"Skipping candle with missing or None close_price: {candle}")
                            continue

                        # Store the candle in the database
                        await self._store_candle(candle)

                        # Check if this is a new interval
                        if not hasattr(self, 'last_start_time') or start_time > self.last_start_time:
                            if hasattr(self, 'last_start_time'):
                                # Previous candle is complete, enrich it
                                await self._process_complete_candle()
                            self.last_start_time = start_time

                    except Exception as e:
                        logger.error(
                            f"Error processing OHLC update {ohlc_update}: {e}", exc_info=True)
                        continue

            else:
                logger.warning(f"Received unexpected data format: {ohlc_data}")

        except Exception as e:
            logger.error(f"Error processing OHLC data: {e}", exc_info=True)

    async def _store_candle(self, candle):
        query = """
        INSERT INTO kraken_ohlc (timestamp, pair, open_price, high_price, low_price, close_price, volume)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (timestamp, pair) DO UPDATE
        SET open_price = EXCLUDED.open_price,
            high_price = EXCLUDED.high_price,
            low_price = EXCLUDED.low_price,
            close_price = EXCLUDED.close_price,
            volume = EXCLUDED.volume
        """
        try:
            self.db.execute_insert(query, (
                candle['timestamp'], self.pair, candle['open_price'],
                candle['high_price'], candle['low_price'], candle['close_price'], candle['volume']
            ))
            logger.debug(
                f"Upserted candle for {candle['timestamp']} into database")

            # Check if we need to retrain the PPO model (if strategy is PPOStrategy)
            if hasattr(self.strategy, '__class__') and self.strategy.__class__.__name__ == 'PPOStrategy':
                await self._check_ppo_training_schedule(candle['timestamp'])

        except Exception as e:
            logger.error(
                f"Failed to upsert candle for {candle['timestamp']}: {e}")
            raise

    async def _check_ppo_training_schedule(self, current_time: datetime):
        """
        Check if it's time to retrain the PPO model based on the 7-day schedule.

        Args:
            current_time (datetime): The current timestamp
        """
        # Skip if strategy is not PPOStrategy
        if not hasattr(self.strategy, '__class__') or self.strategy.__class__.__name__ != 'PPOStrategy':
            return

        # Import PPOStrategy for type checking
        from app.strategy.ppo_strategy import PPOStrategy

        # Type cast for IDE type checking
        if not isinstance(self.strategy, PPOStrategy):
            return

        ppo_strategy = self.strategy  # Now IDE knows this is a PPOStrategy

        # Initialize last_training_time if not set
        if not hasattr(ppo_strategy, 'last_training_time') or ppo_strategy.last_training_time is None:
            ppo_strategy.last_training_time = current_time
            logger.info(
                f"Initialized PPO strategy last_training_time to {current_time}")
            return

        # Check if 7 days have passed since the last training
        days_since_training = (
            current_time - ppo_strategy.last_training_time).days
        # Default to 7 if not set
        retrain_interval = getattr(ppo_strategy, 'retrain_interval_days', 7)

        if days_since_training >= retrain_interval:
            logger.info(
                f"Retraining interval reached ({retrain_interval} days). Initiating PPO model retraining.")

            # Retrieve all historical data from the database (excluding the most recent candle)
            try:
                # Get all historical data up to but not including the current candle
                historical_data = await self._get_all_historical_data(end_time=current_time)

                if historical_data is not None and len(historical_data) > 0:
                    # Train the model with complete historical data
                    logger.info(
                        f"Training PPO model with {len(historical_data)} historical candles")
                    if hasattr(ppo_strategy, '_train_model') and callable(getattr(ppo_strategy, '_train_model')):
                        ppo_strategy._train_model(historical_data)

                        # After training, reset the strategy's historical_data to only contain the most recent 1000 candles
                        if hasattr(ppo_strategy, 'reset_historical_data') and callable(getattr(ppo_strategy, 'reset_historical_data')):
                            ppo_strategy.reset_historical_data(historical_data)

                        # Update the last training time
                        ppo_strategy.last_training_time = current_time
                        logger.info(
                            f"PPO model training complete. Next training scheduled in {retrain_interval} days.")
                    else:
                        logger.error(
                            "PPO strategy does not have a _train_model method")
                else:
                    logger.warning(
                        "No historical data available for PPO model training")
            except Exception as e:
                logger.error(f"Error during PPO model training: {e}")

    async def _get_all_historical_data(self, end_time: datetime) -> pd.DataFrame:
        """
        Retrieve all historical data from the database up to end_time.

        Args:
            end_time (datetime): The end time for the historical data query

        Returns:
            pd.DataFrame: Preprocessed historical data
        """
        # Query to get all historical data up to but not including the current candle
        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp < %s
        ORDER BY timestamp ASC
        """

        try:
            logger.info(
                f"Retrieving all historical data for {self.pair} up to {end_time}")
            historical_data = self.db.execute_select(
                query, (self.pair, end_time), float_columns=list(range(1, 6)))

            if not historical_data:
                logger.warning("No historical data found in database")
                return pd.DataFrame()

            df = pd.DataFrame(historical_data, columns=['timestamp', 'open_price', 'high_price',
                                                        'low_price', 'close_price', 'volume'])

            # Preprocess the data using the strategy's preprocess_data method
            processed_df = self.strategy.preprocess_data(df)
            logger.info(
                f"Retrieved and preprocessed {len(processed_df)} candles from database")
            return processed_df

        except Exception as e:
            logger.error(f"Error retrieving historical data: {e}")
            return pd.DataFrame()

    def on_book_data(self, book_message):
        """
        Handle incoming order book data from the WebSocket.
        This is a synchronous callback that delegates to an async processor.

        Args:
            book_message: The order book data message received from the WebSocket
        """
        asyncio.create_task(self._process_book_data_safe(book_message))

    async def _process_book_data_safe(self, book_message):
        """
        Wrapper that ensures only one _process_book_data runs at a time.

        Args:
            book_message: The order book data message to be processed
        """
        async with self._processing_lock:
            await self._process_book_data(book_message)

    async def _process_book_data(self, book_message):
        """
        Process order book data asynchronously.
        Handles both snapshots and incremental updates.

        Args:
            book_message: The order book data message to process
        """
        try:
            logger.debug(
                f"Processing order book data: {json.dumps(book_message, default=str)}")

            # Handle different message formats
            if not isinstance(book_message, dict):
                logger.warning(
                    f"Received non-dict order book data: {book_message}")
                return

            # Extract book updates based on message format
            book_updates = []
            if 'data' in book_message and isinstance(book_message['data'], list):
                # Batch updates or snapshot with data array
                book_updates = book_message['data']
            elif 'bids' in book_message or 'asks' in book_message:
                # Single update or snapshot
                book_updates = [book_message]
            else:
                logger.warning(
                    f"Unexpected order book message format: {book_message}")
                return

            timestamp = datetime.now(timezone.utc)
            # Default to 'update' if type not specified
            book_type = book_message.get('type', 'update')

            for book_update in book_updates:
                if not isinstance(book_update, dict):
                    logger.warning(
                        f"Skipping invalid book update (not a dict): {book_update}")
                    continue

                # Extract symbol from different possible locations
                symbol = (
                    book_update.get('symbol') or
                    book_message.get('symbol') or
                    book_update.get('pair') or
                    book_message.get('pair') or
                    self.pair
                )
                if not symbol:
                    logger.warning(
                        f"No symbol found in order book update: {book_update}")
                    continue

                # Handle different formats for bids/asks
                bids = book_update.get('bids', [])
                asks = book_update.get('asks', [])
                checksum = book_update.get('checksum')

                # Determine update type (snapshot or update) based on message content
                update_type = book_update.get('type')
                if update_type is None:
                    # If type not specified, guess based on content
                    if 'bids' in book_update and 'asks' in book_update and len(book_update['bids']) > 10:
                        update_type = 'snapshot'
                    else:
                        update_type = 'update'

                # Convert to list of dicts if needed
                def process_levels(levels, is_bid=False):
                    if not levels:
                        return []
                    if isinstance(levels[0], (list, tuple)):
                        return [{'price': float(p[0]), 'qty': float(p[1]), 'timestamp': p[2] if len(p) > 2 else None, 'is_bid': is_bid}
                                for p in levels]
                    # Ensure is_bid is set for dict inputs
                    if isinstance(levels[0], dict):
                        for level in levels:
                            level['is_bid'] = is_bid
                    return levels

                # Process the update
                processed_bids = process_levels(bids, True)
                processed_asks = process_levels(asks, False)

                if update_type == 'snapshot':
                    # For snapshots, completely replace the order book
                    self.order_book['bids'] = {
                        level['price']: level for level in processed_bids}
                    self.order_book['asks'] = {
                        level['price']: level for level in processed_asks}
                    logger.debug(
                        f"Reset order book with {len(processed_bids)} bids and {len(processed_asks)} asks")
                else:
                    # For updates, apply changes incrementally
                    for level in processed_bids:
                        if level['qty'] == 0:
                            self.order_book['bids'].pop(level['price'], None)
                        else:
                            self.order_book['bids'][level['price']] = level

                    for level in processed_asks:
                        if level['qty'] == 0:
                            self.order_book['asks'].pop(level['price'], None)
                        else:
                            self.order_book['asks'][level['price']] = level
                    logger.debug(
                        f"Updated order book: {len(processed_bids)} bid updates, {len(processed_asks)} ask updates")

                # Get the current state for database storage
                bids = sorted(
                    self.order_book['bids'].values(), key=lambda x: -x['price'])
                asks = sorted(
                    self.order_book['asks'].values(), key=lambda x: x['price'])

                # Only store snapshots in the last 5 minutes of each hour
                now = datetime.now(timezone.utc)
                minutes_to_hour = 60 - now.minute

                if minutes_to_hour <= 3:  # Last 3 minutes of the hour
                    bids_json = json.dumps(bids) if bids else '[]'
                    asks_json = json.dumps(asks) if asks else '[]'

                    try:
                        insert_query = """
                        INSERT INTO order_book_snapshots
                            (pair, snapshot_time, bids, asks, checksum)
                        VALUES (%s, %s, %s, %s, %s)
                        """

                        self.db.execute_insert(
                            insert_query,
                            (
                                symbol,
                                timestamp,
                                bids_json,
                                asks_json,
                                checksum
                            )
                        )
                        logger.debug(f"Saved order book for {symbol} at {timestamp} "
                                     f"with {len(bids)} bids and {len(asks)} asks")
                    except Exception as e:
                        logger.error(f"Error saving order book snapshot: {e}")

        except Exception as e:
            logger.error(
                f"Error processing order book data: {e}", exc_info=True)

    async def _process_complete_candle(self):
        """Process a complete candle interval"""
        # Fetch the most recent minute-level data from the database
        end_time = self.last_start_time
        # Fetch enough data for EMA
        start_time = end_time - timedelta(minutes=self.interval_minutes * 200)

        query = """
        SELECT timestamp AT TIME ZONE 'UTC', open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp BETWEEN %s AND %s
        ORDER BY timestamp ASC
        """
        float_columns = list(range(1, 6))
        historical_data = self.db.execute_select(
            query, (self.pair, start_time, end_time), float_columns=float_columns)

        if historical_data:
            df = pd.DataFrame(historical_data, columns=[
                              'timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'])
            enriched_df = self.strategy.preprocess_data(
                df)  # Resamples to hourly and adds EMAs

            if not enriched_df.empty:
                # Get the last (most recent) enriched candle
                last_candle = enriched_df.iloc[-1].to_dict()

                # Ensure the timestamp is timezone-aware
                if last_candle['timestamp'].tzinfo is None:
                    last_candle['timestamp'] = last_candle['timestamp'].replace(
                        tzinfo=timezone.utc)

                # Add the enriched candle to the queue
                try:
                    if self.candle_queue.full():
                        logger.warning(
                            "Candle queue is full, dropping oldest candle")
                        self.candle_queue.get_nowait()  # Remove the oldest item
                        self.candle_queue.task_done()
                    await self.candle_queue.put(last_candle)
                    queue_size = self.candle_queue.qsize()
                    logger.info(
                        f"Added enriched candle for {last_candle['timestamp']} to queue. Queue size: {queue_size}/{self.max_queue_size}")
                except Exception as e:
                    logger.error(f"Error adding candle to queue: {e}")

    async def get_next_candle(self) -> Optional[pd.Series]:
        """Get next candle with simplified connection handling"""
        while True:
            try:
                # Use short timeout to check connection status
                candle = await asyncio.wait_for(
                    self.candle_queue.get(),
                    timeout=60.0
                )
                # Defensive check for None candle
                if candle is None:
                    logger.warning("Received None candle from queue")
                    continue
                # Defensive check for candle missing 'close_price' attribute
                if not hasattr(candle, 'close_price') and (isinstance(candle, dict) and 'close_price' not in candle):
                    logger.warning(
                        "Candle missing 'close_price' attribute, skipping")
                    continue
                # Convert dict to pd.Series if needed
                if isinstance(candle, dict):
                    candle = pd.Series(candle)
                return candle

            except asyncio.TimeoutError:
                # Periodic connection check
                if not self.connected:
                    logger.info(
                        "WebSocket disconnected. Waiting for reconnection...")

    async def get_latest_order_book(self) -> Optional[dict]:
        """
        Get the latest order book snapshot from the database.
        """
        query = """
        SELECT pair, snapshot_time, bids, asks, checksum
        FROM order_book_snapshots
        WHERE pair = %s
        ORDER BY snapshot_time DESC
        LIMIT 1
        """
        try:
            order_book_data = self.db.execute_select(query, (self.pair,))
            if order_book_data:
                row = order_book_data[0]
                return {
                    'pair': row[0],
                    'snapshot_time': row[1],
                    'bids': json.loads(row[2]),
                    'asks': json.loads(row[3]),
                    'checksum': row[4]
                }
            return None
        except Exception as e:
            logger.error(f"Error retrieving latest order book: {e}")
            return None

    async def get_order_book_history(self, start_time: datetime, end_time: datetime) -> list[dict]:
        """
        Get historical order book snapshots for a specific time range.
        """
        query = """
        SELECT pair, snapshot_time, bids, asks, checksum
        FROM order_book_snapshots
        WHERE pair = %s AND snapshot_time BETWEEN %s AND %s
        ORDER BY snapshot_time ASC
        """
        try:
            order_book_history = self.db.execute_select(
                query, (self.pair, start_time, end_time))
            results = []
            for row in order_book_history:
                results.append({
                    'pair': row[0],
                    'snapshot_time': row[1],
                    'bids': json.loads(row[2]),
                    'asks': json.loads(row[3]),
                    'checksum': row[4]
                })
            return results
        except Exception as e:
            logger.error(f"Error retrieving order book history: {e}")
            return []

    async def disconnect(self):
        if self.ws.is_public_connected() and self.ws.public_ws is not None:
            await self.ws.public_ws.close()
            self._connection_event.clear()

    async def health_check(self):
        """
        Periodically check the health of the WebSocket connection.
        Can be run as a background task.
        """
        while True:
            # The new client handles reconnection automatically. This is just for monitoring.
            if not self.ws.is_public_connected():
                logger.warning(
                    "Health check: Public WebSocket is not connected. Auto-reconnect should be active.")
            await asyncio.sleep(60)  # Check every minute
