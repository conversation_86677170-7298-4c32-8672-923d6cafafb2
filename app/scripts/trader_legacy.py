# import asyncio - unused import
import websockets
import json
import requests
import hmac
import base64
import hashlib
import time
import urllib.parse
import psycopg
# from datetime import datetime - unused import
from os import getenv
# from app.exchange.kraken_api import KrakenAPI - unused import
from app.exchange.kraken_websocket import KrakenWebSocket
from ksLib.environment import Environment

e = Environment()
e.load_generic_environment_files()
env = getenv("ENVIRONMENT")

# Kraken API credentials
API_KEY = getenv('KRAKEN_API_KEY')
API_SECRET = getenv('KRAKEN_API_SECRET')

# PostgreSQL connection details
DB_NAME = getenv('DB_NAME')
DB_USER = getenv('DB_USER')
DB_PASSWORD = getenv('DB_PASSWORD')
DB_HOST = getenv('DB_HOST')
DB_PORT = getenv('DB_PORT')

# Kraken API URL
API_URL = getenv('API_URL')
WS_URL = getenv('WS_URL')

# Database connection
conn = psycopg.connect(
    dbname=DB_NAME,
    user=DB_USER,
    password=DB_PASSWORD,
    host=DB_HOST,
    port=DB_PORT
)
cur = conn.cursor()

# Create tables if they don't exist
cur.execute("""
    CREATE TABLE IF NOT EXISTS kraken_trades (
    id SERIAL PRIMARY KEY,
    trade_id BIGINT NOT NULL,  -- Unique ID for each trade
    pair VARCHAR(10) NOT NULL,  -- Trading pair (e.g., 'XBTUSD')
    price NUMERIC(20, 10) NOT NULL,  -- Price at which the trade was executed
    volume NUMERIC(20, 10) NOT NULL,  -- Volume of the trade in base currency
    time TIMESTAMPTZ NOT NULL,  -- Time of the trade in UTC
    side VARCHAR(4) NOT NULL,  -- 'buy' or 'sell'
    UNIQUE (trade_id)  -- Ensures no duplicate trade IDs
);
""")
conn.commit()

def get_kraken_signature(urlpath, data, secret):
    postdata = urllib.parse.urlencode(data)
    encoded = (str(data['nonce']) + postdata).encode()
    message = urlpath.encode() + hashlib.sha256(encoded).digest()
    mac = hmac.new(base64.b64decode(secret), message, hashlib.sha512)
    sigdigest = base64.b64encode(mac.digest())
    return sigdigest.decode()

def kraken_request(uri_path, data, api_key, api_sec):
    headers = {}
    headers['API-Key'] = api_key
    headers['API-Sign'] = get_kraken_signature(uri_path, data, api_sec)
    req = requests.post((API_URL + uri_path), headers=headers, data=data)
    return req.json()

async def handle_trade(msg):
    trade_data = msg['data']
    for trade in trade_data:
        # Unpack values but mark unused ones with underscore prefix
        price, volume, time, _side, _order_type, _misc = trade
        # Create a unique ID (not used but kept for reference)
        # Suppress unused variable warnings by using them in a no-op context
        _ = (_side, _order_type, _misc, f"{time}-{price}-{volume}")
    #    cur.execute("""
    #        INSERT INTO trades (trade_id, pair, time, price, volume)
    #        VALUES (%s, %s, %s, %s, %s)
    #    """, (trade_id, msg['pair'], datetime.fromtimestamp(time), price, volume))
    #conn.commit()

async def subscribe_to_trades(ws):
    await ws.send(json.dumps({
        "event": "subscribe",
        "pair": ["XBT/USD"],
        "subscription": {"name": "trade"}
    }))

def get_historical_trades(pair, since):
    data = {
        "nonce": str(int(1000*time.time())),
        "pair": pair,
        "since": since
    }
    response = kraken_request('/0/public/Trades', data, API_KEY, API_SECRET)
    if 'result' in response and pair in response['result']:
        trades = response['result'][pair]
        for trade in trades:
            # Just print the trade without unpacking to avoid unused variables
            print(trade)
#            cur.execute("""
#                INSERT INTO trades (trade_id, pair, time, price, volume)
#                VALUES (%s, %s, %s, %s, %s)
#                ON CONFLICT (trade_id) DO NOTHING
#            """, (trade_id, pair, datetime.fromtimestamp(time), price, volume))
#        conn.commit()

def place_order(pair, type, side, volume, price=None):
    data = {
        "nonce": str(int(1000*time.time())),
        "ordertype": type,
        "type": side,
        "volume": volume,
        "pair": pair,
    }
    if price:
        data["price"] = price
    response = kraken_request('/0/private/AddOrder', data, API_KEY, API_SECRET)
    if 'result' in response and 'txid' in response['result']:
        return response['result']['txid'][0]
    else:
        raise Exception(f"Failed to place order: {response}")

def cancel_order(txid):
    data = {
        "nonce": str(int(1000*time.time())),
        "txid": txid,
    }
    response = kraken_request('/0/private/CancelOrder', data, API_KEY, API_SECRET)
    if 'result' in response and 'count' in response['result']:
        return response['result']['count']
    else:
        raise Exception(f"Failed to cancel order: {response}")

async def main():
    kraken_ws = KrakenWebSocket()
    await kraken_ws.connect()

    # Subscribe to Level 3 order book for BTC/USD
    #await kraken_ws.subscribe_level3(["BTC/USD"], handle_level3_data)

    # Subscribe to 1-minute OHLC data for BTC/USD
    #await kraken_ws.subscribe_ohlc(["BTC/USD"], 1, handle_ohlc_data)

    # Start handling messages
    await kraken_ws.handle_messages()
    async with websockets.connect(WS_URL or "wss://ws.kraken.com") as ws:
        await subscribe_to_trades(ws)
        while True:
            msg = await ws.recv()
            msg = json.loads(msg)
            if isinstance(msg, list) and msg[2] == 'trade':
                await handle_trade({"pair": msg[3], "data": msg[1]})

if __name__ == "__main__":

    # Get historical trades before starting live feed
    get_historical_trades('SOL/EUR', int(time.time() - 86400) * 1000000000)  # Last 24 hours

    # Run the main loop
    #asyncio.run(main())