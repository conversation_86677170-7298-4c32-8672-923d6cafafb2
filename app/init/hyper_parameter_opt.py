import pandas as pd
import numpy as np
import logging
import os
import json
from datetime import datetime
from stable_baselines3 import PPO
import shutil
import optuna
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler

import sys
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

# Import your modules
from app.strategy.ppo_strategy import PPOStrategy
from app.strategy.trading_env import TradingEnv

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prepare_data(file_path):
    """Load and prepare data with indicators"""
    logger.info(f"Loading data from {file_path}")

    # Load data
    df = pd.read_csv(file_path)

    # Make sure the columns are correctly named
    expected_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

    # Rename columns if needed
    column_mapping = {}
    for expected, actual in zip(expected_columns, df.columns):
        column_mapping[actual] = expected

    df = df.rename(columns=column_mapping)

    # Convert timestamp to datetime if it's not already
    if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

    # Normalize column names to match what the strategy expects
    df = df.rename(columns={
        'open': 'open_price',
        'high': 'high_price',
        'low': 'low_price',
        'close': 'close_price'
    })

    df = PPOStrategy(pair="SOLUSD").preprocess_data(df)
    return df

def objective(trial, train_df, val_df, pair_name, n_timesteps=30000):
    """Optuna objective function for hyperparameter optimization"""
    learning_rate = trial.suggest_float("learning_rate", 1e-4, 3e-4, log=True)
    n_steps = trial.suggest_int("n_steps", 2048, 3072, step=256)
    batch_size = trial.suggest_categorical("batch_size", [64, 96, 128])
    n_epochs = trial.suggest_int("n_epochs", 8, 12)
    gamma = trial.suggest_float("gamma", 0.99, 0.995)
    gae_lambda = trial.suggest_float("gae_lambda", 0.90, 0.98)
    clip_range = trial.suggest_float("clip_range", 0.15, 0.25)
    ent_coef = trial.suggest_float("ent_coef", 0.02, 0.05)
    vf_coef = trial.suggest_float("vf_coef", 0.4, 0.6)
    max_grad_norm = trial.suggest_float("max_grad_norm", 0.3, 0.7)

    # Create environment
    train_env = TradingEnv(train_df.copy(), PPOStrategy(pair_name))

    # Create and train model
    model = PPO(
        "MlpPolicy",
        train_env,
        learning_rate=learning_rate,
        n_steps=n_steps,
        batch_size=batch_size,
        n_epochs=n_epochs,
        gamma=gamma,
        gae_lambda=gae_lambda,
        clip_range=clip_range,
        vf_coef=vf_coef,
        max_grad_norm=max_grad_norm,
        ent_coef=ent_coef,
        verbose=0
    )

    # Train model
    model.learn(total_timesteps=n_timesteps)

    # Evaluate on validation set
    val_env = TradingEnv(val_df.copy(), PPOStrategy(pair_name))
    obs, _ = val_env.reset()
    done = False

    while not done:
        action_array, _ = model.predict(obs, deterministic=True)
        # Convert ndarray to int for step function
        action = int(action_array[0]) if hasattr(action_array, '__len__') else int(action_array)
        obs, _, terminated, truncated, _ = val_env.step(action)  # Updated to handle updated gym API, reward not used
        done = terminated or truncated

    # Get final performance (higher is better)
    final_net_worth = val_env.net_worth_history[-1]

    # Calculate Sharpe ratio
    returns = pd.Series(val_env.net_worth_history).pct_change().dropna()
    if len(returns) > 0 and returns.std() > 0:
        sharpe = returns.mean() / returns.std() * np.sqrt(252)
    else:
        sharpe = -1.0

    # Calculate drawdown
    drawdown = (pd.Series(val_env.net_worth_history) / pd.Series(val_env.net_worth_history).cummax() - 1).min()

    # Combined score: balance between return and risk
    score = final_net_worth * (1 + sharpe) * (1 - abs(drawdown) * 5)

    # Log trial result
    logger.info(f"Trial {trial.number}: score={score:.2f}, final_worth={final_net_worth:.2f}, "
                f"sharpe={sharpe:.2f}, drawdown={drawdown:.2%}")

    return score

def run_optimization(data_file, pair_name, n_trials=50, n_timesteps=30000):
    """Run hyperparameter optimization"""
    # Load and prepare data
    df = prepare_data(data_file)

    # Split data into train/val/test
    train_size = int(len(df) * 0.7)
    val_size = int(len(df) * 0.15)

    train_df = df.iloc[:train_size].copy()
    val_df = df.iloc[train_size:train_size+val_size].copy()
    test_df = df.iloc[train_size+val_size:].copy()

    logger.info(f"Data splits - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")

    # Create Optuna study
    study = optuna.create_study(
        study_name=f"ppo_trading_{pair_name}",
        direction="maximize",
        sampler=TPESampler(),
        pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
    )

    # Run optimization
    study.optimize(
        lambda trial: objective(trial, train_df, val_df, pair_name, n_timesteps),
        n_trials=n_trials
    )

    # Get best parameters
    best_params = study.best_params
    logger.info(f"Best parameters: {best_params}")
    logger.info(f"Best score: {study.best_value}")

    # Save best parameters
    os.makedirs("./hyperparams", exist_ok=True)
    with open(f"./hyperparams/ppo_{pair_name}_{datetime.now().strftime('%Y%m%d')}.json", "w") as f:
        json.dump(best_params, f, indent=2)

    # Train a final model with best parameters
    train_final_model(train_df, val_df, test_df, pair_name, best_params)

def train_final_model(train_df, val_df, test_df, pair_name, best_params, n_timesteps=50000):
    """Train final model with best parameters"""
    # Combine train and validation data for final training
    final_train_df = pd.concat([train_df, val_df])

    # Create environment
    train_env = TradingEnv(final_train_df, PPOStrategy(pair_name))

    # Create model
    model = PPO(
        "MlpPolicy",
        train_env,
        verbose=1,
        **best_params
    )

    # Train model
    model.learn(total_timesteps=n_timesteps)

    # Save model
    os.makedirs("./models", exist_ok=True)
    model_path = f"./models/ppo_{pair_name}_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    model.save(model_path)
    logger.info(f"Optimized model saved to {model_path}")

    # Evaluate on test set
    test_env = TradingEnv(test_df, PPOStrategy(pair_name))
    obs, _ = test_env.reset()
    done = False

    while not done:
        action_array, _ = model.predict(obs, deterministic=True)
        # Convert ndarray to int for step function
        action = int(action_array[0]) if hasattr(action_array, '__len__') else int(action_array)
        obs, _, terminated, truncated, _ = test_env.step(action)  # Updated to handle updated gym API
        done = terminated or truncated

    # Calculate metrics
    final_worth = test_env.net_worth_history[-1]
    returns = pd.Series(test_env.net_worth_history).pct_change().dropna()
    sharpe = returns.mean() / returns.std() * np.sqrt(252) if len(returns) > 0 and returns.std() > 0 else 0
    drawdown = (pd.Series(test_env.net_worth_history) / pd.Series(test_env.net_worth_history).cummax() - 1).min()

    # Log results
    logger.info(f"Test results - Final worth: {final_worth:.2f}, Return: {(final_worth/5000-1)*100:.2f}%, "
                f"Sharpe: {sharpe:.2f}, Max Drawdown: {drawdown:.2%}")

    return model_path

if __name__ == "__main__":
    # Hardcoded variables instead of command-line args
    data_file = "/Users/<USER>/Business/Software projects/pyTrader/ohlc_data/SOLUSD_60.csv"  # Replace with your actual data path
    pair_name = "SOLUSD"                  # Replace with your trading pair
    n_trials = 50                         # Number of optimization trials
    n_timesteps = 30000                   # Training timesteps per trial

    # Create necessary directories
    os.makedirs("./hyperparams", exist_ok=True)
    os.makedirs("./models", exist_ok=True)
    os.makedirs("./tensorboard_logs", exist_ok=True)

    # Print configuration
    print("\n" + "="*50)
    print(f"Starting PPO Hyperparameter Optimization for {pair_name}")
    print("="*50)
    print(f"Data file: {data_file}")
    print(f"Number of trials: {n_trials}")
    print(f"Timesteps per trial: {n_timesteps}")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*50 + "\n")

    try:
        # Run the optimization process
        run_optimization(
            data_file=data_file,
            pair_name=pair_name,
            n_trials=n_trials,
            n_timesteps=n_timesteps
        )

        print("\n" + "="*50)
        print("Optimization completed successfully!")
        print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*50)

        log_dir = "tensorboard_logs"
        try:
            shutil.rmtree(log_dir)
            print(f"Successfully deleted {log_dir} and all its contents")
        except FileNotFoundError:
            print(f"Directory {log_dir} does not exist")
        except Exception as e:
            print(f"Error deleting {log_dir}: {e}")


    except Exception as e:
        logger.error(f"Optimization failed: {str(e)}")
        print("\n" + "="*50)
        print("Optimization failed with error:")
        print(str(e))
        print("="*50)
        sys.exit(1)