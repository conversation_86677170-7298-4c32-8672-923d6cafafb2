"""
Enhanced PPO Model Training Script with Database Integration

This script trains a PPO (Proximal Policy Optimization) model for trading using either:
1. Database source: OHLC data + Order book snapshots from PostgreSQL database
2. CSV fallback: Traditional CSV file loading

Key Features:
- Loads OHLC data from kraken_ohlc table
- Fetches order book snapshots from order_book_snapshots table
- Calculates order book features directly from raw snapshots (spread, depth, volume imbalance, etc.)
- Merges order book features with OHLC data for enhanced PPO training
- Automatic fallback to CSV if database loading fails
- Robust data validation and error handling
"""

from stable_baselines3 import PPO
import pandas as pd
import numpy as np
import logging
import shutil
import asyncio
import json
from datetime import datetime, timezone
import sys
import os
project_root = os.path.abspath(os.path.join(
    os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)
from app.strategy.trading_env import TradingEnv
from app.strategy.ppo_strategy import PPOStrategy
from app.scripts.historical_data_feed import HistoricalDataFeed
from app.db.db_executor import DatabaseExecutor


# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def get_database_connection():
    """Initialize database connection with your database credentials"""
    db_name = os.getenv('DB_NAME')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_host = os.getenv('DB_HOST')
    db_port = int(os.getenv('DB_PORT', '5432'))

    return DatabaseExecutor(
        db_name=db_name,
        db_user=db_user,
        db_password=db_password,
        db_host=db_host,
        db_port=db_port,
        logger=logger
    )


async def fetch_order_book_snapshots(db: DatabaseExecutor, pair: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
    """
    Fetch order book snapshots from database and return as DataFrame with calculated features.
    Uses raw snapshots directly without any preprocessing/merging.

    Args:
        db: Database executor instance
        pair: Trading pair (e.g., 'SOLUSD')
        start_time: Start datetime for data range
        end_time: End datetime for data range

    Returns:
        DataFrame with timestamp and order book features
    """
    query = """
    SELECT pair, snapshot_time, bids, asks, checksum
    FROM order_book_snapshots
    WHERE pair = %s AND snapshot_time BETWEEN %s AND %s
    ORDER BY snapshot_time ASC
    """

    try:
        # Execute query in thread to avoid blocking
        results = await asyncio.to_thread(
            db.execute_select,
            query,
            (pair, start_time, end_time)
        )

        if not results:
            logger.warning(f"No order book snapshots found for {pair} between {start_time} and {end_time}")
            return pd.DataFrame()

        logger.info(f"Retrieved {len(results)} raw order book snapshots for {pair}")

        # First, create DataFrame with raw snapshots for preprocessing
        raw_records = []
        for row in results:
            snapshot_time = row[1]
            bids = row[2]
            if isinstance(bids, str):
                bids = json.loads(bids)
            elif not isinstance(bids, list):
                bids = []

            asks = row[3]
            if isinstance(asks, str):
                asks = json.loads(asks)
            elif not isinstance(asks, list):
                asks = []

            raw_records.append({
                'timestamp': snapshot_time,
                'bids': bids,
                'asks': asks,
                'pair': row[0],
                'checksum': row[4]
            })

        # Calculate order book features directly from raw snapshots (no preprocessing/merging)
        logger.info("Processing order book snapshots directly without merging...")
        feature_records = []
        ppo_strategy = PPOStrategy(pair=pair)  # Use for feature calculation

        for record in raw_records:
            try:
                # Create orderbook_data dict in expected format
                orderbook_data = {
                    'pair': record['pair'],
                    'snapshot_time': record['timestamp'],
                    'bids': record['bids'],
                    'asks': record['asks'],
                    'checksum': record['checksum']
                }

                # Calculate order book features using PPO strategy method
                ob_features = ppo_strategy.calculate_orderbook_features(orderbook_data)

                # Add timestamp for merging with OHLC data
                ob_features['timestamp'] = record['timestamp']
                feature_records.append(ob_features)

            except Exception as e:
                logger.error(f"Error processing order book snapshot at {record.get('timestamp', 'unknown')}: {e}")
                continue

        features_df = pd.DataFrame(feature_records)
        logger.info(f"Calculated order book features for {len(features_df)} snapshots")

        # Log some statistics about the features
        if not features_df.empty:
            logger.info(f"Order book feature statistics:")
            logger.info(f"  - Average spread: {features_df['ob_spread'].mean():.6f}")
            logger.info(f"  - Spread range: [{features_df['ob_spread'].min():.6f}, {features_df['ob_spread'].max():.6f}]")
            logger.info(f"  - Average spread %: {features_df['ob_spread_pct'].mean():.4f}%")
            logger.info(f"  - Volume imbalance range: [{features_df['ob_volume_imbalance'].min():.3f}, {features_df['ob_volume_imbalance'].max():.3f}]")

            # Check for negative spreads (should not happen)
            negative_spreads = features_df[features_df['ob_spread'] < 0]
            if not negative_spreads.empty:
                logger.error(f"Found {len(negative_spreads)} negative spreads! This indicates data quality issues.")
                logger.error(f"Sample negative spreads: {negative_spreads['ob_spread'].head().tolist()}")

            # Check for zero spreads (might indicate stale data)
            zero_spreads = features_df[features_df['ob_spread'] == 0]
            if not zero_spreads.empty:
                logger.warning(f"Found {len(zero_spreads)} zero spreads (might indicate stale data)")
        else:
            logger.warning("No order book features calculated!")

        return features_df

    except Exception as e:
        logger.error(f"Error fetching order book snapshots: {e}")
        return pd.DataFrame()


async def load_and_prepare_data_from_db(db: DatabaseExecutor, pair_name: str, start_date: datetime, end_date: datetime, include_order_book: bool = True):
    """Load and prepare OHLC data from database with optional order book features"""
    logger.info(f"Loading data from database for {pair_name} from {start_date} to {end_date}")

    # Create order book fetcher function if requested
    order_book_fetcher = None
    if include_order_book:
        async def ob_fetcher(start_time, end_time):
            return await fetch_order_book_snapshots(db, pair_name, start_time, end_time)
        order_book_fetcher = ob_fetcher

    # Create PPO strategy for preprocessing
    ppo_strategy = PPOStrategy(pair=pair_name)

    # Create historical data feed
    data_feed = HistoricalDataFeed(
        db=db,
        pair=pair_name,
        start_date=start_date,
        end_date=end_date,
        preprocess_func=ppo_strategy.preprocess_data,
        order_book_fetcher=order_book_fetcher
    )

    # Connect and fetch data
    await data_feed.connect()

    if data_feed.candles is None or data_feed.candles.empty:
        raise ValueError(f"No data found for {pair_name} between {start_date} and {end_date}")

    logger.info(f"Loaded {len(data_feed.candles)} candles with {'order book features' if include_order_book else 'OHLC data only'}")
    return data_feed.candles


def load_and_prepare_data_from_csv(file_path, pair_name):
    """Load and prepare the OHLC data from CSV file (legacy method)"""
    logger.info(f"Loading data from CSV file: {file_path}")

    # Load your Kraken OHLC data
    df = pd.read_csv(file_path)

    # Make sure the columns are correctly named
    expected_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

    # Rename columns if needed
    column_mapping = {}
    for expected, actual in zip(expected_columns, df.columns):
        column_mapping[actual] = expected

    df = df.rename(columns=column_mapping)

    # Convert timestamp to datetime if it's not already
    if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

    # Normalize column names to match what the strategy expects
    df = df.rename(columns={
        'open': 'open_price',
        'high': 'high_price',
        'low': 'low_price',
        'close': 'close_price'
    })

    # Use the PPO strategy's preprocessing logic
    ppo_strategy = PPOStrategy(pair=pair_name)
    df = ppo_strategy.preprocess_data(df)

    return df


def train_model(df, pair_name, timesteps=100000, hyperparams=None):
    """Train the PPO model on the provided data"""
    logger.info(
        f"Starting training for {pair_name} with {len(df)} data points")

    # Create training environment
    train_env = TradingEnv(df, PPOStrategy(pair_name))

    # Default hyperparameters
    default_params = {
        "learning_rate": 0.0003,
        "n_steps": 2048,
        "batch_size": 64,
        "n_epochs": 10,
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2,
        "ent_coef": 0.01,
        "vf_coef": 0.5,
        "max_grad_norm": 0.5

    }

    # Use provided hyperparams or defaults
    params = hyperparams if hyperparams else default_params

    # Create directory for tensorboard logs
    log_dir = f"./tensorboard_logs/{pair_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(log_dir, exist_ok=True)

    # Create directory for models
    model_dir = "./models"
    os.makedirs(model_dir, exist_ok=True)

    # Create and train the model
    model = PPO(
        "MlpPolicy",
        train_env,
        verbose=1,
        **params,
        tensorboard_log=None
    )

    try:
        logger.info(f"Training for {timesteps} timesteps...")
        model.learn(total_timesteps=timesteps)

        # Save the trained model
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        f_name = pair_name.replace("/", "")
        model_save_path = f"{model_dir}/ppo_{f_name}_{timestamp}.zip"
        model.save(model_save_path)
        logger.info(f"Model saved to {model_save_path}")

        return model, None  # model_save_path
    except Exception as e:
        logger.error(f"Error during training: {e}")
        return None, None


def evaluate_model(model, test_df, pair_name):
    """Evaluate the model on test data"""
    logger.info(
        f"Evaluating model for {pair_name} on {len(test_df)} data points")

    # Create test environment
    test_env = TradingEnv(test_df, PPOStrategy(
        pair_name), initial_balance=5000)

    # Run a single episode
    obs, _ = test_env.reset()  # Updated to handle updated gym API
    done = False
    total_reward = 0

    while not done:
        action_array, _ = model.predict(obs, deterministic=True)
        # Convert ndarray to int for step function
        # Handle both scalar (0-dimensional) and array cases
        try:
            # Try to access as array first
            if hasattr(action_array, 'ndim') and action_array.ndim == 0:
                # 0-dimensional numpy array
                action = int(action_array.item())
            elif hasattr(action_array, '__len__') and len(action_array) > 0:
                # Multi-dimensional array
                action = int(action_array[0])
            else:
                # Scalar value
                action = int(action_array)
        except (IndexError, TypeError):
            # Fallback: treat as scalar
            action = int(action_array)
        obs, reward, terminated, truncated, _ = test_env.step(
            action)  # Updated to handle updated gym API
        done = terminated or truncated
        total_reward += reward

        if test_env.current_step % 100 == 0:
            logger.info(test_env.render())

    # Calculate metrics
    final_net_worth = test_env.net_worth_history[-1]
    net_worth_series = pd.Series(test_env.net_worth_history)

    # Calculate returns
    returns = net_worth_series.pct_change().dropna()

    # Calculate metrics
    sharpe_ratio = returns.mean() / (returns.std() + 1e-9) * \
        np.sqrt(252)  # Annualized, avoid div by zero
    max_drawdown = (net_worth_series / net_worth_series.cummax() - 1).min()

    results = {
        "final_net_worth": final_net_worth,
        "total_return": (final_net_worth / 5000 - 1) * 100,  # Percentage
        "sharpe_ratio": sharpe_ratio,
        "max_drawdown": max_drawdown * 100,  # Percentage
        "total_reward": total_reward
    }

    logger.info(f"Evaluation results: {results}")
    return results


async def main():
    # ===== CONFIGURATION SECTION =====

    # Trading pair configuration
    pair_name = "SOL/USD"  # Update this to match your pair

    # Data source configuration
    use_database = True  # Set to True to use database, False to use CSV
    include_order_book_features = True  # Include order book features from database

    # CSV fallback configuration (used if database fails or use_database=False)
    data_file = "/Users/<USER>/Business/Software projects/pyTrader/ohlc_data/SOLUSD_60.csv"

    # Date range for database loading (adjust as needed)
    # Note: Make sure you have data in your database for this range
    start_date = datetime(2021, 6, 17, tzinfo=timezone.utc)
    end_date = datetime(2024, 3, 31, tzinfo=timezone.utc)

    # Training configuration
    train_test_split = 0.9  # 80% for training, 20% for testing
    training_timesteps = 100000  # Adjust based on your dataset size and computation resources

    # ===== END CONFIGURATION SECTION =====

    # Define linear_schedule function
    def linear_schedule(initial_value, final_value):
        """Returns a function that computes a linear schedule."""
        def schedule(progress_remaining):
            return initial_value + (final_value - initial_value) * (1 - progress_remaining)
        return schedule

    # Custom hyperparameters (optional)
    hyperparams = {
        "learning_rate": linear_schedule(0.0003, 0.00005),
        "n_steps": 2048,
        "batch_size": 64,
        "n_epochs": 10,
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2,
        "ent_coef": 0.01,
        "vf_coef": 0.5,
        "max_grad_norm": 0.5

    }

    # Load and prepare data
    if use_database:
        logger.info(f"Loading data from database for {pair_name}...")
        logger.info(f"Date range: {start_date} to {end_date}")
        logger.info(f"Order book features: {'Enabled (raw snapshots)' if include_order_book_features else 'Disabled'}")

        try:
            # Initialize database connection
            db = get_database_connection()

            # Load data from database with order book features
            df = await load_and_prepare_data_from_db(
                db=db,
                pair_name=pair_name,
                start_date=start_date,
                end_date=end_date,
                include_order_book=include_order_book_features
            )

            # Close database connection
            db.close()

            # Log feature information
            order_book_columns = [col for col in df.columns if col.startswith('ob_')]
            if order_book_columns:
                logger.info(f"Order book features included: {order_book_columns}")
            else:
                logger.info("No order book features found in data")

        except Exception as e:
            logger.error(f"Failed to load data from database: {e}")
            logger.info("Falling back to CSV file...")
            df = load_and_prepare_data_from_csv(data_file, pair_name)
    else:
        logger.info("Loading data from CSV file...")
        df = load_and_prepare_data_from_csv(data_file, pair_name)

    # Log final dataset information
    logger.info(f"Final dataset shape: {df.shape}")
    logger.info(f"Columns: {list(df.columns)}")
    logger.info(f"Date range in data: {df['timestamp'].min()} to {df['timestamp'].max()}")

    # Split into training and testing sets
    split_idx = int(len(df) * train_test_split)
    train_df = df.iloc[:split_idx].copy()
    test_df = df.iloc[split_idx:].copy()

    logger.info(
        f"Training data: {len(train_df)} rows, Testing data: {len(test_df)} rows")

    # Add these lines to log start/end times
    logger.info(
        f"Training period: {train_df['timestamp'].iloc[0]} to {train_df['timestamp'].iloc[-1]}")
    logger.info(
        f"Testing period: {test_df['timestamp'].iloc[0]} to {test_df['timestamp'].iloc[-1]}")

    # Train the model
    model, model_path = train_model(
        train_df, pair_name, training_timesteps, hyperparams)
    log_dir = "tensorboard_logs"
    try:
        shutil.rmtree(log_dir)
        print(f"Successfully deleted {log_dir} and all its contents")
    except FileNotFoundError:
        print(f"Directory {log_dir} does not exist")
    except Exception as e:
        print(f"Error deleting {log_dir}: {e}")

    if model:
        # Evaluate the model
        eval_results = evaluate_model(model, test_df, pair_name)

        # Print evaluation results
        print("\n===== Evaluation Results =====")
        print(f"Final Net Worth: ${eval_results['final_net_worth']:.2f}")
        print(f"Total Return: {eval_results['total_return']:.2f}%")
        print(f"Sharpe Ratio: {eval_results['sharpe_ratio']:.4f}")
        print(f"Max Drawdown: {eval_results['max_drawdown']:.2f}%")
        print(f"Total Reward: {eval_results['total_reward']:.4f}")

        print(f"\nTrained model saved to: {model_path}")
        print("You can now use this model in the PPO strategy by setting model_path to this location.")


if __name__ == "__main__":
    asyncio.run(main())
