import os
import sys
import logging
import csv
from os import getenv
from datetime import datetime, timezone, timedelta
from ksLib.environment import Environment
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from db.db_executor import DatabaseExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def get_first_ohlc_timestamp(db: DatabaseExecutor, pair: str):
    # Query to get the first available timestamp for the pair
    query = """
    SELECT MIN(timestamp)
    FROM kraken_ohlc
    WHERE pair = %s
    """
    result = db.execute_select(query, (pair,))
    if result:
        return result[0][0]  # First column of the first row (earliest timestamp)
    return None

def get_max_timestamp(db: DatabaseExecutor, pair: str) -> datetime:
    query = "SELECT MAX(timestamp) FROM kraken_ohlc WHERE pair = %s;"
    result = db.execute_select(query, (pair,))
    max_timestamp = result[0][0] if result and result[0][0] else datetime.min.replace(tzinfo=timezone.utc)
    return max_timestamp.replace(tzinfo=timezone.utc)  # Ensure timezone-aware

def insert_ohlc_from_csv(csv_file: str, pair: str, db: DatabaseExecutor):
    max_timestamp = get_max_timestamp(db, pair)
    logger.info(f"Max timestamp in database for {pair}: {max_timestamp}")

    insert_query = (
        "INSERT INTO kraken_ohlc (timestamp, open_price, high_price, "
        "low_price, close_price, volume, pair) "
        "VALUES (%s, %s, %s, %s, %s, %s, %s) "
        "ON CONFLICT (timestamp, pair) DO NOTHING;"
    )

    rows_inserted = 0
    rows_skipped = 0

    with open(csv_file, 'r') as file:
        reader = csv.reader(file)
        for row in reader:
            timestamp = datetime.fromtimestamp(int(row[0]), tz=timezone.utc)
            #timestamp = datetime.fromisoformat(row[0]).replace(tzinfo=timezone.utc)
            if timestamp <= max_timestamp:
                rows_skipped += 1
                continue

            open_price = float(row[1])
            high_price = float(row[2])
            low_price = float(row[3])
            close_price = float(row[4])
            volume = float(row[5])

            affected_rows = db.execute_insert(insert_query, (timestamp, open_price, high_price, low_price, close_price, volume, pair))
            rows_inserted += affected_rows

    logger.info(f"Inserted {rows_inserted} new rows for {pair}")
    logger.info(f"Skipped {rows_skipped} rows (already in database or older than max timestamp)")

def fill_missing_ohlc_data(db: DatabaseExecutor, pair: str, start_date: datetime, end_date: datetime):
    logger.info(f"Filling missing OHLC data for {pair} from {start_date} to {end_date}")

    # Query to get existing data
    query = """
    SELECT timestamp, open_price, high_price, low_price, close_price, volume
    FROM kraken_ohlc
    WHERE pair = %s AND timestamp BETWEEN %s AND %s
    ORDER BY timestamp
    """
    existing_data = db.execute_select(query, (pair, start_date, end_date))

    if not existing_data:
        logger.warning(f"No existing data found for {pair} in the specified date range.")
        return

    # Convert to dictionary for easier lookup
    existing_data_dict = {row[0]: row[1:] for row in existing_data}

    current_time = start_date
    last_known_data = None
    interpolated_data = []

    while current_time <= end_date:
        if current_time not in existing_data_dict:
            # If last_known_data is available, use it to fill missing data
            if last_known_data is not None:
                try:
                    interpolated_open = last_known_data[1]  # Last known close becomes new open
                    interpolated_close = interpolated_open  # Close is initially set to open
                    interpolated_high = interpolated_open  # Start high as open
                    interpolated_low = interpolated_open   # Start low as open
                    interpolated_volume = 0  # Initially zero until next known volume is available

                    # Increment until we hit the next known timestamp or end date
                    next_time = current_time
                    while next_time not in existing_data_dict and next_time <= end_date:
                        next_time += timedelta(hours=1)

                    # Check if we reached a known data point
                    if next_time in existing_data_dict:
                        next_data = existing_data_dict[next_time]
                        if len(next_data) == 5:  # Check if next_data has the correct length
                            interpolated_close = (interpolated_open + next_data[3]) / 2  # Average with next close
                            interpolated_high = max(interpolated_high, next_data[2])  # Update high
                            interpolated_low = min(interpolated_low, next_data[1])   # Update low
                            # Convert last_known_data[5] (which is Decimal) to float for division
                            # Calculate time difference in seconds and convert to hours
                            time_diff_seconds = (next_time.timestamp() - last_known_data[0].timestamp())
                            time_diff_hours = time_diff_seconds / 3600
                            # Handle volume calculation safely
                            try:
                                # Ensure we're working with a numeric value for volume
                                if isinstance(last_known_data[5], (int, float, str)):
                                    last_volume = float(last_known_data[5])
                                    interpolated_volume = last_volume / time_diff_hours if time_diff_hours > 0 else 0
                                else:
                                    interpolated_volume = 0
                            except (TypeError, ValueError, IndexError):
                                # Fallback if there's any issue with the conversion
                                interpolated_volume = 0
                        else:
                            logger.error(f"Unexpected data format for next_data: {next_data}")
                            continue  # Skip to the next missing timestamp

                    # Append the interpolated data
                    interpolated_data.append((
                        current_time, interpolated_open, interpolated_high,
                        interpolated_low, interpolated_close, interpolated_volume, pair
                    ))

                except IndexError as e:
                    logger.error(f"An error occurred during filling missing data: {e}")
                    logger.debug(f"Last known data: {last_known_data}")
                    logger.debug(f"Current time: {current_time}")
                    # No need to reference next_time which might be unbound
                    logger.debug("Next time: Not available due to error")
                    continue  # Skip to the next missing timestamp

        else:
            last_known_data = (current_time,) + existing_data_dict[current_time]

        current_time += timedelta(minutes=60)

    # Insert interpolated data
    if interpolated_data:
        insert_query = """
        INSERT INTO kraken_ohlc (timestamp, open_price, high_price, low_price, close_price, volume, pair)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (timestamp, pair) DO NOTHING
        """
        db.execute_insert_many(insert_query, interpolated_data)
        logger.info(f"Inserted {len(interpolated_data)} interpolated data points for {pair}")
    else:
        logger.info(f"No missing data points to interpolate for {pair}")

if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    e = Environment()
    e.load_generic_environment_files()
    env = getenv("ENVIRONMENT")

    # PostgreSQL connection details
    DB_NAME = getenv('DB_NAME')
    DB_USER = getenv('DB_USER')
    DB_PASSWORD = getenv('DB_PASSWORD')
    DB_HOST = getenv('DB_HOST')
    DB_PORT = getenv('DB_PORT')

    db = DatabaseExecutor(DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT)
    file = 'ohlc_data/solusd_1h_2025.csv'
    pair = 'SOLUSD'

    try:
        first_timestamp = get_first_ohlc_timestamp(db, pair)
        insert_ohlc_from_csv(file, pair, db)
        logger.info('OHLC data import completed successfully!')

        end_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        #end_date = end_date - timedelta(days=19)
        #start_date = end_date - timedelta(days=7)
        # Make sure start_date is a valid datetime object
        if first_timestamp is not None:
            start_date = first_timestamp
            fill_missing_ohlc_data(db, pair, start_date, end_date)
        else:
            logger.error("No first timestamp found, cannot fill missing data")
        logger.info('Missing OHLC data filled successfully!')
    except Exception as e:
        logger.error(f"An error occurred during data import: {str(e)}")
