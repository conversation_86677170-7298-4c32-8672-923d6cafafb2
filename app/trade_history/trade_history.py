import logging
from typing import List, Optional
from datetime import datetime
from app.db.db_executor import DatabaseExecutor
from .trade_action import TradeAction
from ..trade_manager.result import Result

class TradeHistory:
    def __init__(self, mode: str, db: Optional[DatabaseExecutor], logger: logging.Logger) -> None:
        """
        Initialize TradeHistory with trading mode and database executor.

        Args:
            mode: 'backtest', 'paper', or 'live' to determine the database table.
            db: DatabaseExecutor instance for saving data. Can be None for in-memory only operation.
            logger: Logger instance for logging messages.
        """
        self.mode = mode
        self.db = db
        self.trade_actions: List[TradeAction] = []
        self.trade_results: List[Result] = []
        self.consecutive_losses = 0
        self.logger = logger

    def log_action(self, trade_id: str, trade_id_match: str, timestamp: datetime, action_type: str,
                   price: float, volume: float, fee: float, reason: str, strategy_name: str, pair: str):
        action = TradeAction(
            trade_id_match=trade_id_match,
            trade_id=trade_id,
            timestamp=timestamp,
            action_type=action_type,
            price=price,
            volume=volume,
            value=round(price * volume, 2),
            fee=fee,
            reason=reason,
            strategy_name=strategy_name,
            pair=pair
        )
        self.trade_actions.append(action)
        # Save to database immediately
        self.save_action_to_db(action)

    def log_result(self, strategy_name: str, entry_trade_id_match: str, exit_trade_id_match: str,
                   entry_price: float, exit_price: float, entry_fee: float, exit_fee: float,
                   entry_timestamp: datetime, exit_timestamp: datetime,
                   volume: float, profit_loss: float, duration, success: bool, exit_reason: str):
        result = Result(
            strategy_name=strategy_name,
            entry_trade_id=entry_trade_id_match,
            exit_trade_id=exit_trade_id_match,
            entry_price=entry_price,
            exit_price=exit_price,
            entry_fee=entry_fee,
            exit_fee=exit_fee,
            entry_timestamp=entry_timestamp,
            exit_timestamp=exit_timestamp,
            volume=volume,
            profit_loss=profit_loss,
            duration=duration,
            success=success,
            exit_reason=exit_reason
        )
        if profit_loss < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0

        self.trade_results.append(result)
        # Save to database immediately
        self.save_result_to_db(result)

    def save_action_to_db(self, action: TradeAction):
        table_name = f"{self.mode}_trades"
        query = f"""
        INSERT INTO {table_name} (trade_id, trade_id_match, price, volume, value, timestamp, pair, buy_sell, fee, reason)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        if self.db:
            self.db.execute_insert(query, (
                action.trade_id,
                action.trade_id_match,
                action.price,
                action.volume,
                action.value,
                action.timestamp,
                action.pair,
                action.action_type,
                action.fee,
                action.reason #,
    #            action.strategy_name
            ))

    def save_result_to_db(self, result: Result):
        table_name = f"{self.mode}_results"
        query = f"""
        INSERT INTO {table_name} (strategy_name, entry_trade_id, exit_trade_id, entry_price, exit_price, entry_fee, exit_fee,
                                  entry_timestamp, exit_timestamp, volume, profit_loss, duration, success, exit_reason)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        if self.db:
            self.db.execute_insert(query, (
                result.strategy_name,
                result.entry_trade_id,
                result.exit_trade_id,
                result.entry_price,
                result.exit_price,
                result.entry_fee,
                result.exit_fee,
                result.entry_timestamp,
                result.exit_timestamp,
                result.volume,
                result.profit_loss,
                result.duration,
                result.success,
                result.exit_reason
            ))

    def get_trade_actions(self) -> List[TradeAction]:
        return self.trade_actions

    def get_trade_results(self) -> List[Result]:
        return self.trade_results

    def clear_previous_results(self):
        self.logger.info("Clearing previous backtest results...")
        if self.db:
            delete_trades_query = "DELETE FROM backtest_trades"
            delete_results_query = "DELETE FROM backtest_results"
            self.db.execute_delete(delete_trades_query)
            self.db.execute_delete(delete_results_query)
            self.logger.info("Previous backtest results cleared.")
        else:
            self.logger.info("No database connection, skipping database clear.")
