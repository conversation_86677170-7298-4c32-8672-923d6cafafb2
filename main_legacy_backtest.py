import traceback
import os
import logging
from datetime import datetime
from ksLib.environment import Environment
from app.backtester.backtestAnalyzer import BacktestAnalyzer
from app.db.db_executor import DatabaseExecutor
from app.backtester.backtester import Backtester
from app.strategy.original_ema_strategy import EMAStrategy
from app.trade_manager.trade_manager import TradeManager
from app.trade_manager.portfolio_manager import PortfolioManager

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    # Load environment variables
    e = Environment()
    e.load_generic_environment_files()
    env = os.getenv("ENVIRONMENT")

    DB_NAME = os.getenv('DB_NAME')
    DB_USER = os.getenv('DB_USER')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_HOST = os.getenv('DB_HOST')
    DB_PORT = os.getenv('DB_PORT')

    db = DatabaseExecutor(DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT)

    # Strategy parameters
    ema_short = 7
    ema_long = 21
    pair = 'SOLUSD'
    initial_portfolio = 5000
    investment_percentage = 0.8
    max_investment = 7500
    taker_maker_fee = 0.0025
    take_profit_levels = [0.25, 0.5, 0.7]
    stop_loss = -0.2
    min_investment = 200
    sell_at_target = 0.25
    interval = '1h'

    start_date = datetime(2021, 6, 18)
    end_date = datetime(2024, 12, 31)

    #start_date = datetime(2021, 6, 18)
    #end_date = datetime(2021, 11, 1)

    #start_date = datetime(2021, 11, 1)
    #end_date = datetime(2022, 7, 1)

    #start_date = datetime(2022, 7, 1)
    #end_date = datetime(2023, 10, 1)

    #start_date = datetime(2023, 10, 1)
    #end_date = datetime(2024, 12, 31)


    try:
        strategy = EMAStrategy(
            pair=pair,
            short_window=ema_short,
            long_window=ema_long,
            take_profit_levels=take_profit_levels,
            stop_loss=stop_loss,
            interval=interval,
            investment_percentage=investment_percentage,
            taker_maker_fee=taker_maker_fee,
            max_investment=max_investment,
            min_investment=min_investment
        )

        portfolio_manager = PortfolioManager(initial_portfolio)

        trade_manager = TradeManager(portfolio_manager=portfolio_manager)

        backtester = Backtester(db, strategy, pair, start_date, end_date, trade_manager, portfolio_manager)
        df = backtester.get_ohlc_data()
        backtester.clear_previous_results()
        trades, results = backtester.run_backtest(df)

        backtester.save_results(trades, results)

        strategy_name = strategy.__class__.__name__
        analyzer = BacktestAnalyzer()
        analyzer.analyze_results(db, strategy_name, initial_portfolio, pair)

    except Exception as e:
        logger.error(f"An error occurred during strategy execution: {str(e)}")
        tb_str = traceback.format_exc()
        print(tb_str)